#!/usr/bin/env tsx

/**
 * 更新搜索相关服务以使用SearchConfig而不是FieldConfig
 * 这个脚本会：
 * 1. 更新configCache服务
 * 2. 创建新的搜索服务适配器
 * 3. 更新API路由
 * 4. 保持向后兼容性
 */

import { writeFileSync, readFileSync, existsSync } from 'fs';
import { join } from 'path';

interface UpdateOptions {
  dryRun: boolean;
  verbose: boolean;
}

async function main() {
  const options: UpdateOptions = {
    dryRun: process.argv.includes('--dry-run'),
    verbose: process.argv.includes('--verbose')
  };

  console.log('🔧 开始更新搜索服务...');
  console.log(`模式: ${options.dryRun ? '预览模式' : '执行模式'}`);
  console.log('');

  try {
    // 步骤1: 创建搜索配置适配器
    await createSearchConfigAdapter(options);
    
    // 步骤2: 更新configCache服务
    await updateConfigCacheService(options);
    
    // 步骤3: 创建新的API路由
    await createSearchConfigAPI(options);
    
    // 步骤4: 更新前端组件
    await updateFrontendComponents(options);
    
    console.log('\n✅ 搜索服务更新完成！');
    
  } catch (error) {
    console.error('❌ 更新失败:', error);
    process.exit(1);
  }
}

async function createSearchConfigAdapter(options: UpdateOptions) {
  console.log('🔌 创建搜索配置适配器...');
  
  const adapterContent = `import { SearchConfig, getSearchConfigsByDatabase, getBasicFilterConfigs } from './searchConfigService';
import { DatabaseFieldConfig } from './configCache';

/**
 * 搜索配置适配器 - 将SearchConfig转换为兼容的FieldConfig格式
 * 用于向后兼容，逐步迁移现有代码
 */

export interface LegacySearchConfig {
  fieldName: string;
  displayName: string;
  fieldType: string;
  isSearchable: boolean;
  isFilterable: boolean;
  isAdvancedSearchable: boolean;
  searchType: string;
  filterType: string;
  options?: Record<string, unknown>;
  validationRules?: Record<string, unknown>;
}

/**
 * 将SearchConfig转换为兼容的字段配置格式
 */
export function convertSearchConfigToFieldConfig(searchConfig: SearchConfig): LegacySearchConfig {
  // 从searchFields中提取字段信息
  const searchFields = searchConfig.searchFields as any;
  const fieldName = searchFields.field || searchFields.fields?.[0] || 'unknown';
  
  return {
    fieldName,
    displayName: searchConfig.name,
    fieldType: searchConfig.filterType === 'date_range' ? 'date' : 'text',
    isSearchable: !searchConfig.isAdvanced && searchConfig.configType === 'SIMPLE_FILTER',
    isFilterable: searchConfig.filterType !== 'input' && !searchConfig.isAdvanced,
    isAdvancedSearchable: searchConfig.isAdvanced,
    searchType: searchFields.searchType || 'contains',
    filterType: searchConfig.filterType,
    options: searchConfig.options,
    validationRules: searchConfig.validationRules
  };
}

/**
 * 获取数据库的搜索字段配置（兼容模式）
 */
export async function getSearchableFields(database: string): Promise<LegacySearchConfig[]> {
  const searchConfigs = await getSearchConfigsByDatabase(database);
  return searchConfigs.map(convertSearchConfigToFieldConfig);
}

/**
 * 获取数据库的过滤字段配置（兼容模式）
 */
export async function getFilterableFields(database: string): Promise<LegacySearchConfig[]> {
  const basicConfigs = await getBasicFilterConfigs(database);
  return basicConfigs
    .filter(config => config.filterType !== 'input')
    .map(convertSearchConfigToFieldConfig);
}

/**
 * 获取数据库的高级搜索字段配置（兼容模式）
 */
export async function getAdvancedSearchableFields(database: string): Promise<LegacySearchConfig[]> {
  const searchConfigs = await getSearchConfigsByDatabase(database);
  return searchConfigs
    .filter(config => config.isAdvanced)
    .map(convertSearchConfigToFieldConfig);
}

/**
 * 混合模式：同时从FieldConfig和SearchConfig获取配置
 * 用于渐进式迁移
 */
export async function getHybridSearchConfig(database: string): Promise<{
  legacy: DatabaseFieldConfig[];
  enhanced: SearchConfig[];
  combined: LegacySearchConfig[];
}> {
  // 获取传统配置
  const { getDatabaseConfig } = await import('./configCache');
  const dbConfig = await getDatabaseConfig(database);
  const legacy = dbConfig.fields;
  
  // 获取增强配置
  const enhanced = await getSearchConfigsByDatabase(database);
  
  // 合并配置
  const enhancedAsLegacy = enhanced.map(convertSearchConfigToFieldConfig);
  const combined = [...legacy, ...enhancedAsLegacy];
  
  return {
    legacy,
    enhanced,
    combined
  };
}`;

  const filePath = 'src/lib/searchConfigAdapter.ts';
  
  if (options.dryRun) {
    console.log(`  [预览] 将创建文件: ${filePath}`);
  } else {
    writeFileSync(filePath, adapterContent);
    console.log(`  ✅ 创建: ${filePath}`);
  }
}

async function updateConfigCacheService(options: UpdateOptions) {
  console.log('📦 更新配置缓存服务...');
  
  const configCachePath = 'src/lib/configCache.ts';
  
  if (!existsSync(configCachePath)) {
    console.log('  ⚠️  配置缓存文件不存在，跳过更新');
    return;
  }
  
  const originalContent = readFileSync(configCachePath, 'utf-8');
  
  // 添加搜索配置支持
  const updatedContent = originalContent.replace(
    /export interface DatabaseFieldConfig \{[\s\S]*?\}/,
    `export interface DatabaseFieldConfig {
  fieldName: string;
  displayName: string;
  fieldType: 'text' | 'date' | 'number' | 'boolean' | 'select' | 'json';
  isVisible: boolean;
  isSearchable: boolean;
  isFilterable: boolean;
  isAdvancedSearchable?: boolean;
  isSortable: boolean;
  sortOrder: number;
  listOrder: number;
  detailOrder: number;
  searchType: 'exact' | 'contains' | 'range' | 'date_range' | 'starts_with' | 'ends_with';
  filterType: 'select' | 'input' | 'date_range' | 'checkbox' | 'multi_select' | 'range';
  validationRules?: Record<string, unknown>;
  options?: Record<string, unknown>;
  todetail?: boolean;
  isActive?: boolean;
  
  // 新增：标记是否来自SearchConfig
  fromSearchConfig?: boolean;
  searchConfigCode?: string;
}`
  );
  
  if (options.dryRun) {
    console.log(`  [预览] 将更新: ${configCachePath}`);
    console.log('    • 添加fromSearchConfig和searchConfigCode字段');
  } else {
    writeFileSync(configCachePath, updatedContent);
    console.log(`  ✅ 更新: ${configCachePath}`);
  }
}

async function createSearchConfigAPI(options: UpdateOptions) {
  console.log('🌐 创建搜索配置API...');
  
  const apiContent = `import { NextRequest, NextResponse } from 'next/server';
import { getSearchConfigsByDatabase, getBasicFilterConfigs } from '@/lib/searchConfigService';
import { getHybridSearchConfig } from '@/lib/searchConfigAdapter';

export const dynamic = 'force-dynamic';

/**
 * GET /api/search-config/[database]
 * 获取数据库的搜索配置
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ database: string }> }
) {
  try {
    const { database } = await params;
    const searchParams = request.nextUrl.searchParams;
    const mode = searchParams.get('mode') || 'enhanced'; // enhanced | legacy | hybrid
    const type = searchParams.get('type'); // basic | advanced | filter
    
    switch (mode) {
      case 'legacy':
        // 返回传统FieldConfig格式（向后兼容）
        const { getDatabaseConfig } = await import('@/lib/configCache');
        const dbConfig = await getDatabaseConfig(database);
        return NextResponse.json({
          success: true,
          data: dbConfig.fields,
          mode: 'legacy'
        });
        
      case 'hybrid':
        // 返回混合配置
        const hybridConfig = await getHybridSearchConfig(database);
        return NextResponse.json({
          success: true,
          data: hybridConfig,
          mode: 'hybrid'
        });
        
      case 'enhanced':
      default:
        // 返回增强SearchConfig格式
        let configs;
        
        if (type === 'basic') {
          configs = await getBasicFilterConfigs(database);
        } else if (type === 'advanced') {
          const allConfigs = await getSearchConfigsByDatabase(database);
          configs = allConfigs.filter(config => config.isAdvanced);
        } else if (type === 'filter') {
          const allConfigs = await getSearchConfigsByDatabase(database);
          configs = allConfigs.filter(config => 
            !config.isAdvanced && config.filterType !== 'input'
          );
        } else {
          configs = await getSearchConfigsByDatabase(database);
        }
        
        return NextResponse.json({
          success: true,
          data: configs,
          mode: 'enhanced',
          type: type || 'all'
        });
    }
    
  } catch (error) {
    console.error('获取搜索配置失败:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/search-config/[database]
 * 创建或更新搜索配置
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ database: string }> }
) {
  try {
    const { database } = await params;
    const body = await request.json();
    
    // 这里可以添加创建/更新搜索配置的逻辑
    // 暂时返回成功响应
    return NextResponse.json({
      success: true,
      message: 'Search config updated successfully'
    });
    
  } catch (error) {
    console.error('更新搜索配置失败:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}`;

  const apiPath = 'src/app/api/search-config/[database]/route.ts';
  
  if (options.dryRun) {
    console.log(`  [预览] 将创建API: ${apiPath}`);
  } else {
    // 确保目录存在
    const { mkdirSync } = require('fs');
    const { dirname } = require('path');
    mkdirSync(dirname(apiPath), { recursive: true });
    
    writeFileSync(apiPath, apiContent);
    console.log(`  ✅ 创建API: ${apiPath}`);
  }
}

async function updateFrontendComponents(options: UpdateOptions) {
  console.log('🎨 更新前端组件...');
  
  // 创建新的搜索面板组件
  const unifiedSearchPanelContent = `"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import EnhancedSearchPanel from './EnhancedSearchPanel';
import AdvancedSearch from './AdvancedSearch';

interface UnifiedSearchPanelProps {
  database: string;
  onSearch: (searchData: any) => void;
  onClear: () => void;
  loading?: boolean;
  className?: string;
  mode?: 'enhanced' | 'legacy' | 'hybrid';
}

export default function UnifiedSearchPanel({
  database,
  onSearch,
  onClear,
  loading = false,
  className = "",
  mode = 'enhanced'
}: UnifiedSearchPanelProps) {
  const [activeTab, setActiveTab] = useState('enhanced');
  const [searchConfigs, setSearchConfigs] = useState<any[]>([]);
  const [legacyFields, setLegacyFields] = useState<any[]>([]);

  useEffect(() => {
    loadConfigurations();
  }, [database, mode]);

  const loadConfigurations = async () => {
    try {
      const response = await fetch(\`/api/search-config/\${database}?mode=\${mode}\`);
      const result = await response.json();
      
      if (result.success) {
        if (mode === 'hybrid') {
          setSearchConfigs(result.data.enhanced);
          setLegacyFields(result.data.legacy);
        } else if (mode === 'enhanced') {
          setSearchConfigs(result.data);
        } else {
          setLegacyFields(result.data);
        }
      }
    } catch (error) {
      console.error('加载搜索配置失败:', error);
    }
  };

  const handleEnhancedSearch = (searchData: any) => {
    onSearch({ type: 'enhanced', data: searchData });
  };

  const handleLegacySearch = (searchData: any) => {
    onSearch({ type: 'legacy', data: searchData });
  };

  if (mode === 'enhanced') {
    return (
      <EnhancedSearchPanel
        database={database}
        onSearch={handleEnhancedSearch}
        onClear={onClear}
        loading={loading}
        className={className}
      />
    );
  }

  if (mode === 'legacy') {
    return (
      <AdvancedSearch
        database={database}
        onSearch={handleLegacySearch}
        onClear={onClear}
        loading={loading}
      />
    );
  }

  // 混合模式
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Search & Filter</span>
          <Badge variant="outline">Hybrid Mode</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="enhanced">Enhanced</TabsTrigger>
            <TabsTrigger value="legacy">Legacy</TabsTrigger>
          </TabsList>
          
          <TabsContent value="enhanced" className="mt-4">
            <EnhancedSearchPanel
              database={database}
              onSearch={handleEnhancedSearch}
              onClear={onClear}
              loading={loading}
              className="border-0 shadow-none"
            />
          </TabsContent>
          
          <TabsContent value="legacy" className="mt-4">
            <AdvancedSearch
              database={database}
              onSearch={handleLegacySearch}
              onClear={onClear}
              loading={loading}
            />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}`;

  const componentPath = 'src/components/UnifiedSearchPanel.tsx';
  
  if (options.dryRun) {
    console.log(`  [预览] 将创建组件: ${componentPath}`);
  } else {
    writeFileSync(componentPath, unifiedSearchPanelContent);
    console.log(`  ✅ 创建组件: ${componentPath}`);
  }
}

// 运行脚本
if (require.main === module) {
  main().catch(console.error);
}

export { main as updateSearchServices };
