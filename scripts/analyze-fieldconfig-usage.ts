import { PrismaClient } from '@prisma/client';

const db = new PrismaClient();

async function analyzeFieldConfigUsage() {
  console.log('🔍 分析 FieldConfig 表字段使用情况...\n');

  try {
    // 1. 获取所有数据库的字段配置
    const allConfigs = await db.fieldConfig.findMany({
      where: { isActive: true },
      select: {
        databaseCode: true,
        fieldName: true,
        displayName: true,
        isVisible: true,
        isSearchable: true,
        isFilterable: true,
        isAdvancedSearchable: true,
        isSortable: true,
        searchType: true,
        filterType: true,
        isStatisticsEnabled: true,
        isExportable: true,
        todetail: true
      }
    });

    console.log(`📊 总共找到 ${allConfigs.length} 个活跃字段配置\n`);

    // 2. 按数据库分组统计
    const byDatabase = allConfigs.reduce((acc, config) => {
      if (!acc[config.databaseCode]) {
        acc[config.databaseCode] = [];
      }
      acc[config.databaseCode].push(config);
      return acc;
    }, {} as Record<string, typeof allConfigs>);

    console.log('📋 各数据库字段配置统计:');
    Object.entries(byDatabase).forEach(([dbCode, configs]) => {
      console.log(`\n🗄️  ${dbCode}:`);
      console.log(`   总字段: ${configs.length}`);
      console.log(`   可见字段: ${configs.filter(c => c.isVisible).length}`);
      console.log(`   可筛选字段: ${configs.filter(c => c.isFilterable).length}`);
      console.log(`   可搜索字段: ${configs.filter(c => c.isSearchable).length}`);
      console.log(`   可高级搜索字段: ${configs.filter(c => c.isAdvancedSearchable).length}`);
      console.log(`   可排序字段: ${configs.filter(c => c.isSortable).length}`);
      console.log(`   统计字段: ${configs.filter(c => c.isStatisticsEnabled).length}`);
      console.log(`   可导出字段: ${configs.filter(c => c.isExportable).length}`);
      console.log(`   详情链接字段: ${configs.filter(c => c.todetail).length}`);
    });

    // 3. 分析字段功能重叠情况
    console.log('\n🔄 字段功能重叠分析:');
    
    const searchableOnly = allConfigs.filter(c => c.isSearchable && !c.isFilterable && !c.isAdvancedSearchable);
    const filterableOnly = allConfigs.filter(c => c.isFilterable && !c.isSearchable && !c.isAdvancedSearchable);
    const advancedSearchableOnly = allConfigs.filter(c => c.isAdvancedSearchable && !c.isSearchable && !c.isFilterable);
    const multiFunction = allConfigs.filter(c => 
      (c.isSearchable ? 1 : 0) + (c.isFilterable ? 1 : 0) + (c.isAdvancedSearchable ? 1 : 0) > 1
    );

    console.log(`   仅可搜索: ${searchableOnly.length} 个字段`);
    console.log(`   仅可筛选: ${filterableOnly.length} 个字段`);
    console.log(`   仅可高级搜索: ${advancedSearchableOnly.length} 个字段`);
    console.log(`   多功能字段: ${multiFunction.length} 个字段`);

    // 4. 检查SearchConfig表的使用情况
    console.log('\n🔍 SearchConfig 表使用情况:');
    
    try {
      const searchConfigs = await db.$queryRaw<any[]>`
        SELECT 
          "configType", 
          "isAdvanced", 
          "isActive",
          COUNT(*) as count
        FROM "SearchConfig"
        GROUP BY "configType", "isAdvanced", "isActive"
        ORDER BY "configType", "isAdvanced"
      `;

      if (searchConfigs.length > 0) {
        console.log('   SearchConfig 配置统计:');
        searchConfigs.forEach(config => {
          const status = config.isActive ? '活跃' : '非活跃';
          const level = config.isAdvanced ? '高级' : '基础';
          console.log(`     ${config.configType} (${level}, ${status}): ${config.count} 个`);
        });
      } else {
        console.log('   ⚠️  SearchConfig 表为空或不存在');
      }
    } catch (error) {
      console.log('   ⚠️  无法访问 SearchConfig 表:', error.message);
    }

    // 5. 分析哪些字段可能是冗余的
    console.log('\n🎯 冗余字段分析:');
    
    console.log('\n当前字段使用情况:');
    console.log('✅ isVisible - 用于控制字段显示 (必需)');
    console.log('✅ isFilterable - 用于左侧筛选面板 (必需)');
    console.log('✅ isSortable - 用于排序功能 (必需)');
    console.log('✅ isStatisticsEnabled - 用于统计功能 (必需)');
    console.log('✅ isExportable - 用于导出功能 (必需)');
    console.log('✅ todetail - 用于详情链接 (必需)');
    
    console.log('\n可能冗余的字段:');
    console.log('❓ isSearchable - 可能被 SearchConfig 替代');
    console.log('❓ isAdvancedSearchable - 可能被 SearchConfig 替代');
    console.log('❓ searchType - 可能被 SearchConfig 替代');
    console.log('❓ filterType - 仍被筛选面板使用，但可能可以简化');

    // 6. 建议
    console.log('\n💡 建议:');
    console.log('1. 保留 isFilterable 和 filterType，因为左侧筛选面板仍在使用');
    console.log('2. 可以考虑逐步迁移 isSearchable 功能到 SearchConfig');
    console.log('3. isAdvancedSearchable 可以作为 SearchConfig 的回退选项保留');
    console.log('4. searchType 在 SearchConfig 存在时可以忽略');
    console.log('5. 建议先完善 SearchConfig 功能，再考虑清理 FieldConfig');

  } catch (error) {
    console.error('❌ 分析过程中发生错误:', error);
  } finally {
    await db.$disconnect();
  }
}

// 运行分析
analyzeFieldConfigUsage().catch(console.error);
