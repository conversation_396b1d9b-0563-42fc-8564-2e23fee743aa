import { PrismaClient } from '@prisma/client';

const db = new PrismaClient();

async function testAdvancedSearchIntegration() {
  console.log('🧪 测试高级搜索集成功能...\n');

  try {
    // 1. 测试API端点
    console.log('1. 测试 /api/config/us_pmn 端点...');
    
    const response = await fetch('http://localhost:3000/api/config/us_pmn');
    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status}`);
    }
    
    const data = await response.json();
    console.log(`✅ API响应成功，包含 ${data.fields.length} 个字段`);
    
    // 2. 验证isAdvancedSearchable字段
    console.log('\n2. 验证 isAdvancedSearchable 字段...');
    
    const fieldsWithAdvancedSearch = data.fields.filter((f: any) => f.isAdvancedSearchable);
    console.log(`✅ 找到 ${fieldsWithAdvancedSearch.length} 个可高级搜索字段`);
    
    // 显示前5个可高级搜索字段
    console.log('前5个可高级搜索字段:');
    fieldsWithAdvancedSearch.slice(0, 5).forEach((field: any, index: number) => {
      console.log(`   ${index + 1}. ${field.fieldName} (${field.displayName})`);
    });
    
    // 3. 验证字段分类
    console.log('\n3. 验证字段分类...');
    
    const searchableFields = data.fields.filter((f: any) => f.isSearchable);
    const filterableFields = data.fields.filter((f: any) => f.isFilterable);
    const advancedSearchableFields = data.fields.filter((f: any) => f.isAdvancedSearchable);
    
    console.log(`   可搜索字段: ${searchableFields.length} 个`);
    console.log(`   可筛选字段: ${filterableFields.length} 个`);
    console.log(`   可高级搜索字段: ${advancedSearchableFields.length} 个`);
    
    // 4. 验证AdvancedSearch组件可用字段逻辑
    console.log('\n4. 验证 AdvancedSearch 组件可用字段逻辑...');
    
    // 模拟AdvancedSearch组件的字段过滤逻辑
    const availableForAdvancedSearch = data.fields.filter((field: any) => 
      field.isSearchable || field.isFilterable || field.isAdvancedSearchable
    );
    
    console.log(`✅ AdvancedSearch 可用字段: ${availableForAdvancedSearch.length} 个`);
    
    // 5. 验证字段功能分布
    console.log('\n5. 字段功能分布分析...');
    
    const onlySearchable = data.fields.filter((f: any) => 
      f.isSearchable && !f.isFilterable && !f.isAdvancedSearchable
    );
    const onlyFilterable = data.fields.filter((f: any) => 
      !f.isSearchable && f.isFilterable && !f.isAdvancedSearchable
    );
    const onlyAdvancedSearchable = data.fields.filter((f: any) => 
      !f.isSearchable && !f.isFilterable && f.isAdvancedSearchable
    );
    const multiFunction = data.fields.filter((f: any) => 
      (f.isSearchable ? 1 : 0) + (f.isFilterable ? 1 : 0) + (f.isAdvancedSearchable ? 1 : 0) > 1
    );
    
    console.log(`   仅可搜索: ${onlySearchable.length} 个`);
    console.log(`   仅可筛选: ${onlyFilterable.length} 个`);
    console.log(`   仅可高级搜索: ${onlyAdvancedSearchable.length} 个`);
    console.log(`   多功能字段: ${multiFunction.length} 个`);
    
    // 6. 显示仅可高级搜索的字段（这些是新增的功能）
    if (onlyAdvancedSearchable.length > 0) {
      console.log('\n6. 仅可高级搜索的字段（新增功能）:');
      onlyAdvancedSearchable.forEach((field: any, index: number) => {
        console.log(`   ${index + 1}. ${field.fieldName} (${field.displayName})`);
      });
    }
    
    // 7. 验证数据库中的配置
    console.log('\n7. 验证数据库配置一致性...');
    
    const dbFields = await db.fieldConfig.findMany({
      where: { 
        databaseCode: 'us_pmn',
        isActive: true 
      },
      select: {
        fieldName: true,
        isSearchable: true,
        isFilterable: true,
        isAdvancedSearchable: true
      }
    });
    
    console.log(`✅ 数据库中找到 ${dbFields.length} 个活跃字段`);
    
    // 验证API和数据库的一致性
    const apiFieldNames = new Set(data.fields.map((f: any) => f.fieldName));
    const dbFieldNames = new Set(dbFields.map(f => f.fieldName));
    
    const missingInApi = dbFields.filter(f => !apiFieldNames.has(f.fieldName));
    const missingInDb = data.fields.filter((f: any) => !dbFieldNames.has(f.fieldName));
    
    if (missingInApi.length === 0 && missingInDb.length === 0) {
      console.log('✅ API和数据库字段完全一致');
    } else {
      console.log(`⚠️  发现不一致: API缺失 ${missingInApi.length} 个，数据库缺失 ${missingInDb.length} 个`);
    }
    
    console.log('\n🎉 高级搜索集成测试完成！');
    
    console.log('\n📋 总结:');
    console.log(`   - Filter面板可用字段: ${filterableFields.length} 个`);
    console.log(`   - Advanced Search可用字段: ${availableForAdvancedSearch.length} 个`);
    console.log(`   - 新增专用高级搜索字段: ${onlyAdvancedSearchable.length} 个`);
    console.log(`   - API和数据库配置一致性: ${missingInApi.length === 0 && missingInDb.length === 0 ? '✅' : '⚠️'}`);

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  } finally {
    await db.$disconnect();
  }
}

// 运行测试
testAdvancedSearchIntegration().catch(console.error);
