#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

/**
 * 重置搜索配置表
 */

async function resetSearchConfig() {
  console.log('🔄 开始重置搜索配置系统...');

  try {
    // 1. 清空现有数据
    console.log('🗑️  清空现有搜索配置...');
    await db.$executeRaw`DELETE FROM "SearchConfig"`;
    
    console.log('✅ 搜索配置已清空');

  } catch (error) {
    console.error('❌ 重置搜索配置失败:', error);
    throw error;
  }
}

// 执行重置
if (require.main === module) {
  resetSearchConfig()
    .then(() => {
      console.log('🎉 搜索配置重置成功！');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 重置失败:', error);
      process.exit(1);
    });
}

export { resetSearchConfig };
