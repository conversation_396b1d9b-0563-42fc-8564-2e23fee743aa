#!/usr/bin/env tsx

/**
 * 智能搜索功能迁移
 * 只使用Next.js代码，不修改数据库结构
 * 将FieldConfig的搜索功能完全迁移到SearchConfig
 */

import { db } from '../src/lib/prisma';
import crypto from 'crypto';

interface MigrationOptions {
  dryRun: boolean;
  verbose: boolean;
}

async function main() {
  const options: MigrationOptions = {
    dryRun: process.argv.includes('--dry-run'),
    verbose: process.argv.includes('--verbose')
  };

  console.log('🚀 智能搜索功能迁移');
  console.log(`模式: ${options.dryRun ? '预览模式' : '执行模式'}`);
  console.log('');

  try {
    // 步骤1: 分析当前状况
    await analyzeCurrentState(options);
    
    // 步骤2: 检查SearchConfig覆盖情况
    await checkSearchConfigCoverage(options);
    
    // 步骤3: 补充缺失的SearchConfig
    await createMissingSearchConfigs(options);
    
    // 步骤4: 清理FieldConfig搜索字段
    await cleanupFieldConfigSearch(options);
    
    // 步骤5: 验证迁移结果
    await verifyMigration(options);
    
    console.log('\n✅ 智能迁移完成！');
    
  } catch (error) {
    console.error('❌ 迁移失败:', error);
    process.exit(1);
  } finally {
    await db.$disconnect();
  }
}

async function analyzeCurrentState(options: MigrationOptions) {
  console.log('🔍 分析当前状况...');
  
  // 获取FieldConfig中的搜索字段
  const searchFields = await db.fieldConfig.findMany({
    where: {
      isActive: true,
      OR: [
        { isSearchable: true },
        { isFilterable: true },
        { isAdvancedSearchable: true }
      ]
    },
    select: {
      databaseCode: true,
      fieldName: true,
      displayName: true,
      isSearchable: true,
      isFilterable: true,
      isAdvancedSearchable: true,
      searchType: true,
      filterType: true,
      options: true
    }
  });
  
  console.log(`  📊 FieldConfig中有 ${searchFields.length} 个搜索相关字段`);
  
  // 按数据库和功能分类
  const stats = searchFields.reduce((acc, field) => {
    const db = field.databaseCode;
    if (!acc[db]) acc[db] = { searchable: 0, filterable: 0, advanced: 0 };
    
    if (field.isSearchable) acc[db].searchable++;
    if (field.isFilterable) acc[db].filterable++;
    if (field.isAdvancedSearchable) acc[db].advanced++;
    
    return acc;
  }, {} as Record<string, { searchable: number; filterable: number; advanced: number }>);
  
  Object.entries(stats).forEach(([dbCode, stat]) => {
    console.log(`    ${dbCode}: 搜索${stat.searchable} + 筛选${stat.filterable} + 高级${stat.advanced}`);
  });
  
  // 获取SearchConfig现状
  const searchConfigs = await db.$queryRaw<any[]>`
    SELECT 
      "configType", 
      "isAdvanced", 
      COUNT(*) as count
    FROM "SearchConfig" 
    WHERE "isActive" = true
    GROUP BY "configType", "isAdvanced"
    ORDER BY "configType", "isAdvanced"
  `;
  
  console.log(`  📊 SearchConfig中有 ${searchConfigs.reduce((sum, c) => sum + Number(c.count), 0)} 个配置`);
  searchConfigs.forEach(config => {
    const level = config.isAdvanced ? '高级' : '基础';
    console.log(`    ${config.configType} (${level}): ${config.count} 个`);
  });
  
  console.log('');
}

async function checkSearchConfigCoverage(options: MigrationOptions) {
  console.log('🔍 检查SearchConfig覆盖情况...');
  
  // 获取所有需要迁移的字段
  const fieldsToMigrate = await db.fieldConfig.findMany({
    where: {
      isActive: true,
      OR: [
        { isSearchable: true },
        { isFilterable: true },
        { isAdvancedSearchable: true }
      ]
    },
    select: {
      databaseCode: true,
      fieldName: true,
      displayName: true,
      isSearchable: true,
      isFilterable: true,
      isAdvancedSearchable: true,
      searchType: true,
      filterType: true,
      options: true
    }
  });
  
  // 获取现有的SearchConfig
  const existingConfigs = await db.$queryRaw<any[]>`
    SELECT code, "targetDatabases", "searchFields", "isAdvanced"
    FROM "SearchConfig" 
    WHERE "isActive" = true
  `;
  
  // 检查覆盖情况
  const missingConfigs = [];
  
  for (const field of fieldsToMigrate) {
    // 检查基础搜索
    if (field.isSearchable) {
      const configCode = `${field.databaseCode}_${field.fieldName}_search`;
      const exists = existingConfigs.some(c => c.code === configCode);
      if (!exists) {
        missingConfigs.push({
          type: 'basic_search',
          field,
          configCode
        });
      }
    }
    
    // 检查筛选功能
    if (field.isFilterable) {
      const configCode = `${field.databaseCode}_${field.fieldName}_filter`;
      const exists = existingConfigs.some(c => c.code === configCode);
      if (!exists) {
        missingConfigs.push({
          type: 'filter',
          field,
          configCode
        });
      }
    }
    
    // 检查高级搜索
    if (field.isAdvancedSearchable) {
      const configCode = `${field.databaseCode}_${field.fieldName}_advanced`;
      const exists = existingConfigs.some(c => c.code === configCode);
      if (!exists) {
        missingConfigs.push({
          type: 'advanced_search',
          field,
          configCode
        });
      }
    }
  }
  
  console.log(`  📊 需要补充 ${missingConfigs.length} 个SearchConfig配置`);
  
  if (options.verbose && missingConfigs.length > 0) {
    missingConfigs.slice(0, 10).forEach(config => {
      console.log(`    • ${config.configCode} (${config.type})`);
    });
    if (missingConfigs.length > 10) {
      console.log(`    ... 还有 ${missingConfigs.length - 10} 个配置`);
    }
  }
  
  console.log('');
  return missingConfigs;
}

async function createMissingSearchConfigs(options: MigrationOptions) {
  console.log('🔧 补充缺失的SearchConfig...');
  
  const missingConfigs = await checkSearchConfigCoverage({ ...options, verbose: false });
  
  if (missingConfigs.length === 0) {
    console.log('  ✅ 所有配置已存在，无需补充');
    console.log('');
    return;
  }
  
  if (options.dryRun) {
    console.log(`  [预览] 将创建 ${missingConfigs.length} 个SearchConfig配置`);
    console.log('');
    return;
  }
  
  let created = 0;
  
  for (const config of missingConfigs) {
    const { field, configCode, type } = config;
    
    try {
      const searchConfigData = {
        id: crypto.randomUUID(),
        code: configCode,
        name: `${field.displayName} ${type === 'filter' ? 'Filter' : type === 'advanced_search' ? 'Advanced Search' : 'Search'}`,
        description: `${type === 'filter' ? 'Filter' : type === 'advanced_search' ? 'Advanced search' : 'Search'} by ${field.displayName} in ${field.databaseCode}`,
        configType: 'SIMPLE_FILTER',
        targetDatabases: JSON.stringify([field.databaseCode]),
        searchFields: JSON.stringify({
          database: field.databaseCode,
          field: field.fieldName,
          searchType: field.searchType || 'contains'
        }),
        displayOrder: created + 1,
        filterType: type === 'filter' ? (field.filterType || 'select') : 'input',
        placeholder: `Enter ${field.displayName.toLowerCase()}...`,
        accessLevel: 'free',
        isActive: true,
        isAdvanced: type === 'advanced_search',
        options: field.options ? JSON.stringify(field.options) : null
      };
      
      await db.$executeRaw`
        INSERT INTO "SearchConfig" (
          "id", "code", "name", "description", "configType", "targetDatabases",
          "searchFields", "displayOrder", "filterType", "placeholder",
          "accessLevel", "isActive", "isAdvanced", "options", "createdAt", "updatedAt"
        ) VALUES (
          ${searchConfigData.id}, ${searchConfigData.code}, ${searchConfigData.name}, 
          ${searchConfigData.description}, ${searchConfigData.configType}, 
          ${searchConfigData.targetDatabases}::jsonb, ${searchConfigData.searchFields}::jsonb,
          ${searchConfigData.displayOrder}, ${searchConfigData.filterType}, 
          ${searchConfigData.placeholder}, ${searchConfigData.accessLevel}, 
          ${searchConfigData.isActive}, ${searchConfigData.isAdvanced},
          ${searchConfigData.options ? searchConfigData.options : null}::jsonb,
          CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
        )
      `;
      
      created++;
      
    } catch (error) {
      console.log(`    ⚠️  创建配置失败: ${configCode} - ${error.message}`);
    }
  }
  
  console.log(`  ✅ 成功创建 ${created} 个SearchConfig配置`);
  console.log('');
}

async function cleanupFieldConfigSearch(options: MigrationOptions) {
  console.log('🧹 清理FieldConfig搜索字段...');
  
  if (options.dryRun) {
    const count = await db.fieldConfig.count({
      where: {
        isActive: true,
        OR: [
          { isSearchable: true },
          { isFilterable: true },
          { isAdvancedSearchable: true }
        ]
      }
    });
    
    console.log(`  [预览] 将清理 ${count} 个字段的搜索功能`);
    console.log('');
    return;
  }
  
  const result = await db.fieldConfig.updateMany({
    where: { isActive: true },
    data: {
      isSearchable: false,
      isFilterable: false,
      isAdvancedSearchable: false,
      searchType: 'contains',
      filterType: 'select',
      filterOrder: 0
    }
  });
  
  console.log(`  ✅ 已清理 ${result.count} 个字段的搜索功能`);
  console.log('  📝 保留功能: 显示、排序、统计、导出等');
  console.log('');
}

async function verifyMigration(options: MigrationOptions) {
  console.log('🔍 验证迁移结果...');
  
  if (options.dryRun) {
    console.log('  [预览模式] 跳过验证');
    console.log('');
    return;
  }
  
  // 检查FieldConfig清理情况
  const remainingSearch = await db.fieldConfig.count({
    where: {
      isActive: true,
      OR: [
        { isSearchable: true },
        { isFilterable: true },
        { isAdvancedSearchable: true }
      ]
    }
  });
  
  // 检查SearchConfig总数
  const totalSearchConfigs = await db.$queryRaw<[{count: string}]>`
    SELECT COUNT(*) as count FROM "SearchConfig" WHERE "isActive" = true
  `;
  
  console.log(`  📊 迁移结果:`);
  console.log(`    FieldConfig剩余搜索字段: ${remainingSearch} 个`);
  console.log(`    SearchConfig总配置: ${totalSearchConfigs[0].count} 个`);
  
  if (remainingSearch === 0) {
    console.log('  ✅ 搜索功能迁移完成');
  } else {
    console.log('  ⚠️  仍有搜索字段未清理');
  }
  
  console.log('');
}

// 运行脚本
if (require.main === module) {
  main().catch(console.error);
}

export { main as smartSearchMigration };
