#!/usr/bin/env tsx

/**
 * 将搜索配置从FieldConfig迁移到SearchConfig
 * 这个脚本会：
 * 1. 分析现有FieldConfig中的搜索配置
 * 2. 创建对应的SearchConfig记录
 * 3. 更新相关代码以使用SearchConfig
 * 4. 保持向后兼容性
 */

import { db } from '../src/lib/prisma';
import crypto from 'crypto';

interface MigrationOptions {
  dryRun: boolean;
  verbose: boolean;
  database?: string; // 可选：只迁移特定数据库
}

interface FieldConfigForMigration {
  id: string;
  databaseCode: string;
  fieldName: string;
  displayName: string;
  fieldType: string;
  isSearchable: boolean;
  isFilterable: boolean;
  isAdvancedSearchable: boolean;
  searchType: string;
  filterType: string;
  options?: any;
  validationRules?: any;
}

async function main() {
  const options: MigrationOptions = {
    dryRun: process.argv.includes('--dry-run'),
    verbose: process.argv.includes('--verbose'),
    database: process.argv.find(arg => arg.startsWith('--database='))?.split('=')[1]
  };

  console.log('🚀 开始搜索配置迁移...');
  console.log(`模式: ${options.dryRun ? '预览模式' : '执行模式'}`);
  if (options.database) {
    console.log(`目标数据库: ${options.database}`);
  }
  console.log('');

  try {
    // 步骤1: 分析现有配置
    await analyzeCurrentConfig(options);
    
    // 步骤2: 创建基础搜索配置
    await createBasicSearchConfigs(options);
    
    // 步骤3: 创建高级搜索配置
    await createAdvancedSearchConfigs(options);
    
    // 步骤4: 创建过滤器配置
    await createFilterConfigs(options);
    
    // 步骤5: 验证迁移结果
    await verifyMigration(options);
    
    console.log('\n✅ 搜索配置迁移完成！');
    
    if (!options.dryRun) {
      console.log('\n📝 下一步操作：');
      console.log('1. 更新前端组件以使用SearchConfig');
      console.log('2. 更新API以使用SearchConfig');
      console.log('3. 从FieldConfig中移除搜索相关字段');
      console.log('4. 清理缓存');
    }
    
  } catch (error) {
    console.error('❌ 迁移失败:', error);
    process.exit(1);
  } finally {
    await db.$disconnect();
  }
}

async function analyzeCurrentConfig(options: MigrationOptions) {
  console.log('📊 分析现有搜索配置...');
  
  const whereClause = options.database ? { databaseCode: options.database } : {};
  
  const fieldConfigs = await db.fieldConfig.findMany({
    where: {
      ...whereClause,
      isActive: true,
      OR: [
        { isSearchable: true },
        { isFilterable: true },
        { isAdvancedSearchable: true }
      ]
    },
    select: {
      id: true,
      databaseCode: true,
      fieldName: true,
      displayName: true,
      fieldType: true,
      isSearchable: true,
      isFilterable: true,
      isAdvancedSearchable: true,
      searchType: true,
      filterType: true,
      options: true,
      validationRules: true
    }
  });

  // 按数据库分组统计
  const stats = fieldConfigs.reduce((acc, config) => {
    if (!acc[config.databaseCode]) {
      acc[config.databaseCode] = {
        total: 0,
        searchable: 0,
        filterable: 0,
        advancedSearchable: 0
      };
    }
    
    acc[config.databaseCode].total++;
    if (config.isSearchable) acc[config.databaseCode].searchable++;
    if (config.isFilterable) acc[config.databaseCode].filterable++;
    if (config.isAdvancedSearchable) acc[config.databaseCode].advancedSearchable++;
    
    return acc;
  }, {} as Record<string, any>);

  console.log('  📈 统计结果:');
  Object.entries(stats).forEach(([db, stat]) => {
    console.log(`    ${db}:`);
    console.log(`      • 总字段: ${stat.total}`);
    console.log(`      • 可搜索: ${stat.searchable}`);
    console.log(`      • 可过滤: ${stat.filterable}`);
    console.log(`      • 高级搜索: ${stat.advancedSearchable}`);
  });
  
  console.log(`  📋 总计: ${fieldConfigs.length} 个字段需要迁移`);
  console.log('');
  
  return fieldConfigs;
}

async function createBasicSearchConfigs(options: MigrationOptions) {
  console.log('🔍 创建基础搜索配置...');
  
  const whereClause = options.database ? { databaseCode: options.database } : {};
  
  const searchableFields = await db.fieldConfig.findMany({
    where: {
      ...whereClause,
      isActive: true,
      isSearchable: true
    }
  });

  let created = 0;
  
  for (const field of searchableFields) {
    const configCode = `${field.databaseCode}_${field.fieldName}_search`;
    const config = {
      id: crypto.randomUUID(),
      code: configCode,
      name: `${field.displayName} Search`,
      description: `Search by ${field.displayName} in ${field.databaseCode} database`,
      configType: 'SIMPLE_FILTER',
      targetDatabases: JSON.stringify([field.databaseCode]),
      searchFields: JSON.stringify({
        database: field.databaseCode,
        field: field.fieldName,
        searchType: field.searchType || 'contains'
      }),
      displayOrder: created + 1,
      filterType: 'input',
      placeholder: `Enter ${field.displayName.toLowerCase()}...`,
      accessLevel: 'free',
      isActive: true,
      isAdvanced: false
    };

    if (options.dryRun) {
      console.log(`  [预览] 将创建: ${config.code} - ${config.name}`);
    } else {
      // 检查是否已存在
      const existing = await db.$queryRaw<any[]>`
        SELECT id FROM "SearchConfig" WHERE code = ${config.code}
      `;
      
      if (existing.length === 0) {
        await db.$executeRaw`
          INSERT INTO "SearchConfig" (
            "id", "code", "name", "description", "configType", "targetDatabases",
            "searchFields", "displayOrder", "filterType", "placeholder",
            "accessLevel", "isActive", "isAdvanced", "createdAt", "updatedAt"
          ) VALUES (
            ${config.id}, ${config.code}, ${config.name}, ${config.description},
            ${config.configType}, ${config.targetDatabases}::jsonb, ${config.searchFields}::jsonb,
            ${config.displayOrder}, ${config.filterType}, ${config.placeholder},
            ${config.accessLevel}, ${config.isActive}, ${config.isAdvanced}, 
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
          )
        `;
        console.log(`  ✅ 创建: ${config.code}`);
        created++;
      } else {
        console.log(`  ⚠️  跳过已存在: ${config.code}`);
      }
    }
  }
  
  console.log(`  📊 基础搜索配置: ${options.dryRun ? '预览' : '创建'} ${created} 个`);
  console.log('');
}

async function createAdvancedSearchConfigs(options: MigrationOptions) {
  console.log('🔬 创建高级搜索配置...');
  
  const whereClause = options.database ? { databaseCode: options.database } : {};
  
  const advancedFields = await db.fieldConfig.findMany({
    where: {
      ...whereClause,
      isActive: true,
      isAdvancedSearchable: true
    }
  });

  let created = 0;
  
  for (const field of advancedFields) {
    const configCode = `${field.databaseCode}_${field.fieldName}_advanced`;
    const config = {
      id: crypto.randomUUID(),
      code: configCode,
      name: `${field.displayName} (Advanced)`,
      description: `Advanced search by ${field.displayName} in ${field.databaseCode} database`,
      configType: 'SIMPLE_FILTER',
      targetDatabases: JSON.stringify([field.databaseCode]),
      searchFields: JSON.stringify({
        database: field.databaseCode,
        field: field.fieldName,
        searchType: field.searchType || 'contains'
      }),
      displayOrder: created + 100, // 高级搜索排在后面
      filterType: field.filterType || 'input',
      placeholder: `Enter ${field.displayName.toLowerCase()}...`,
      accessLevel: 'free',
      isActive: true,
      isAdvanced: true,
      options: field.options,
      validationRules: field.validationRules
    };

    if (options.dryRun) {
      console.log(`  [预览] 将创建: ${config.code} - ${config.name}`);
    } else {
      // 检查是否已存在
      const existing = await db.$queryRaw<any[]>`
        SELECT id FROM "SearchConfig" WHERE code = ${config.code}
      `;
      
      if (existing.length === 0) {
        await db.$executeRaw`
          INSERT INTO "SearchConfig" (
            "id", "code", "name", "description", "configType", "targetDatabases",
            "searchFields", "displayOrder", "filterType", "placeholder",
            "accessLevel", "isActive", "isAdvanced", "options", "validationRules",
            "createdAt", "updatedAt"
          ) VALUES (
            ${config.id}, ${config.code}, ${config.name}, ${config.description},
            ${config.configType}, ${config.targetDatabases}::jsonb, ${config.searchFields}::jsonb,
            ${config.displayOrder}, ${config.filterType}, ${config.placeholder},
            ${config.accessLevel}, ${config.isActive}, ${config.isAdvanced},
            ${config.options ? JSON.stringify(config.options) : null}::jsonb,
            ${config.validationRules ? JSON.stringify(config.validationRules) : null}::jsonb,
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
          )
        `;
        console.log(`  ✅ 创建: ${config.code}`);
        created++;
      } else {
        console.log(`  ⚠️  跳过已存在: ${config.code}`);
      }
    }
  }
  
  console.log(`  📊 高级搜索配置: ${options.dryRun ? '预览' : '创建'} ${created} 个`);
  console.log('');
}

async function createFilterConfigs(options: MigrationOptions) {
  console.log('🎛️ 创建过滤器配置...');
  
  const whereClause = options.database ? { databaseCode: options.database } : {};
  
  const filterableFields = await db.fieldConfig.findMany({
    where: {
      ...whereClause,
      isActive: true,
      isFilterable: true
    }
  });

  let created = 0;
  
  for (const field of filterableFields) {
    const configCode = `${field.databaseCode}_${field.fieldName}_filter`;
    const config = {
      id: crypto.randomUUID(),
      code: configCode,
      name: `${field.displayName} Filter`,
      description: `Filter by ${field.displayName} in ${field.databaseCode} database`,
      configType: 'SIMPLE_FILTER',
      targetDatabases: JSON.stringify([field.databaseCode]),
      searchFields: JSON.stringify({
        database: field.databaseCode,
        field: field.fieldName,
        searchType: field.searchType || 'exact'
      }),
      displayOrder: created + 200, // 过滤器排在最后
      filterType: field.filterType || 'select',
      placeholder: `Select ${field.displayName.toLowerCase()}...`,
      accessLevel: 'free',
      isActive: true,
      isAdvanced: false,
      options: field.options,
      validationRules: field.validationRules
    };

    if (options.dryRun) {
      console.log(`  [预览] 将创建: ${config.code} - ${config.name}`);
    } else {
      // 检查是否已存在
      const existing = await db.$queryRaw<any[]>`
        SELECT id FROM "SearchConfig" WHERE code = ${config.code}
      `;
      
      if (existing.length === 0) {
        await db.$executeRaw`
          INSERT INTO "SearchConfig" (
            "id", "code", "name", "description", "configType", "targetDatabases",
            "searchFields", "displayOrder", "filterType", "placeholder",
            "accessLevel", "isActive", "isAdvanced", "options", "validationRules",
            "createdAt", "updatedAt"
          ) VALUES (
            ${config.id}, ${config.code}, ${config.name}, ${config.description},
            ${config.configType}, ${config.targetDatabases}::jsonb, ${config.searchFields}::jsonb,
            ${config.displayOrder}, ${config.filterType}, ${config.placeholder},
            ${config.accessLevel}, ${config.isActive}, ${config.isAdvanced},
            ${config.options ? JSON.stringify(config.options) : null}::jsonb,
            ${config.validationRules ? JSON.stringify(config.validationRules) : null}::jsonb,
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
          )
        `;
        console.log(`  ✅ 创建: ${config.code}`);
        created++;
      } else {
        console.log(`  ⚠️  跳过已存在: ${config.code}`);
      }
    }
  }
  
  console.log(`  📊 过滤器配置: ${options.dryRun ? '预览' : '创建'} ${created} 个`);
  console.log('');
}

async function verifyMigration(options: MigrationOptions) {
  console.log('🔍 验证迁移结果...');
  
  if (options.dryRun) {
    console.log('  [预览模式] 跳过验证');
    return;
  }
  
  const searchConfigs = await db.$queryRaw<any[]>`
    SELECT 
      "configType", 
      "isAdvanced", 
      COUNT(*) as count
    FROM "SearchConfig" 
    WHERE "isActive" = true
    GROUP BY "configType", "isAdvanced"
    ORDER BY "configType", "isAdvanced"
  `;
  
  console.log('  📊 SearchConfig统计:');
  searchConfigs.forEach(stat => {
    const type = stat.isAdvanced ? 'Advanced' : 'Basic';
    console.log(`    ${stat.configType} (${type}): ${stat.count} 个`);
  });
  
  console.log('  ✅ 迁移验证完成');
  console.log('');
}

// 运行脚本
if (require.main === module) {
  main().catch(console.error);
}

export { main as migrateSearchToSearchConfig };
