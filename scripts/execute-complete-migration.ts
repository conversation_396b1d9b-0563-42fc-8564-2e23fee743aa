#!/usr/bin/env tsx

/**
 * 执行完整的搜索配置迁移
 * 从FieldConfig完全迁移到SearchConfig
 */

import { db } from '../src/lib/prisma';

interface MigrationStep {
  name: string;
  description: string;
  execute: (dryRun: boolean) => Promise<void>;
  verify: () => Promise<boolean>;
}

async function main() {
  const dryRun = process.argv.includes('--dry-run');
  const skipVerification = process.argv.includes('--skip-verification');
  
  console.log('🚀 执行完整搜索配置迁移');
  console.log(`模式: ${dryRun ? '预览模式' : '执行模式'}`);
  console.log('');

  const steps: MigrationStep[] = [
    {
      name: '验证SearchConfig覆盖',
      description: '确保SearchConfig已经覆盖所有搜索功能',
      execute: async (dryRun) => {
        const searchConfigCount = await db.$queryRaw<[{count: string}]>`
          SELECT COUNT(*) as count FROM "SearchConfig" WHERE "isActive" = true
        `;
        
        const count = Number(searchConfigCount[0].count);
        console.log(`  SearchConfig活跃配置: ${count} 个`);
        
        if (count < 50) {
          throw new Error('SearchConfig配置不足，请先运行数据迁移');
        }
      },
      verify: async () => {
        const count = await db.$queryRaw<[{count: string}]>`
          SELECT COUNT(*) as count FROM "SearchConfig" WHERE "isActive" = true
        `;
        return Number(count[0].count) >= 50;
      }
    },
    
    {
      name: '清理FieldConfig搜索字段',
      description: '将FieldConfig中的搜索相关字段设为false',
      execute: async (dryRun) => {
        const searchFields = await db.fieldConfig.count({
          where: {
            isActive: true,
            OR: [
              { isSearchable: true },
              { isAdvancedSearchable: true }
            ]
          }
        });
        
        console.log(`  找到 ${searchFields} 个有搜索功能的字段`);
        
        if (!dryRun) {
          const result = await db.fieldConfig.updateMany({
            where: { isActive: true },
            data: {
              isSearchable: false,
              isAdvancedSearchable: false,
              searchType: 'contains'
            }
          });
          
          console.log(`  ✅ 已清理 ${result.count} 个字段的搜索功能`);
        } else {
          console.log(`  [预览] 将清理 ${searchFields} 个字段的搜索功能`);
        }
      },
      verify: async () => {
        const count = await db.fieldConfig.count({
          where: {
            isActive: true,
            OR: [
              { isSearchable: true },
              { isAdvancedSearchable: true }
            ]
          }
        });
        return count === 0;
      }
    },
    
    {
      name: '验证功能完整性',
      description: '确保所有搜索功能仍然可用',
      execute: async (dryRun) => {
        // 测试基础搜索配置
        const basicConfigs = await db.$queryRaw<any[]>`
          SELECT COUNT(*) as count FROM "SearchConfig" 
          WHERE "isActive" = true AND "isAdvanced" = false
        `;
        
        // 测试高级搜索配置
        const advancedConfigs = await db.$queryRaw<any[]>`
          SELECT COUNT(*) as count FROM "SearchConfig" 
          WHERE "isActive" = true AND "isAdvanced" = true
        `;
        
        console.log(`  基础搜索配置: ${basicConfigs[0].count} 个`);
        console.log(`  高级搜索配置: ${advancedConfigs[0].count} 个`);
        
        // 测试跨表搜索
        const crossTableConfigs = await db.$queryRaw<any[]>`
          SELECT COUNT(*) as count FROM "SearchConfig" 
          WHERE "isActive" = true AND "configType" = 'CROSS_TABLE'
        `;
        
        console.log(`  跨表搜索配置: ${crossTableConfigs[0].count} 个`);
      },
      verify: async () => {
        const totalConfigs = await db.$queryRaw<[{count: string}]>`
          SELECT COUNT(*) as count FROM "SearchConfig" WHERE "isActive" = true
        `;
        return Number(totalConfigs[0].count) > 0;
      }
    }
  ];

  try {
    // 执行迁移步骤
    for (let i = 0; i < steps.length; i++) {
      const step = steps[i];
      console.log(`\n📋 步骤 ${i + 1}: ${step.name}`);
      console.log(`   ${step.description}`);
      
      try {
        await step.execute(dryRun);
        
        if (!skipVerification && !dryRun) {
          const verified = await step.verify();
          if (verified) {
            console.log(`   ✅ 验证通过`);
          } else {
            throw new Error(`步骤 ${i + 1} 验证失败`);
          }
        }
      } catch (error) {
        console.error(`   ❌ 步骤 ${i + 1} 失败:`, error.message);
        throw error;
      }
    }
    
    // 生成迁移总结
    await generateMigrationSummary(dryRun);
    
    console.log('\n🎉 完整迁移执行成功！');
    
    if (dryRun) {
      console.log('\n📝 要执行实际迁移，请运行:');
      console.log('   npx tsx scripts/execute-complete-migration.ts');
    } else {
      console.log('\n📝 迁移完成后的架构:');
      console.log('   • 所有搜索功能使用SearchConfig');
      console.log('   • FieldConfig只保留显示、排序、筛选等功能');
      console.log('   • 代码架构更简洁高效');
      console.log('   • 支持更复杂的搜索场景');
    }
    
  } catch (error) {
    console.error('\n❌ 迁移失败:', error);
    
    if (!dryRun) {
      console.log('\n🔄 回滚建议:');
      console.log('   1. 恢复FieldConfig的搜索字段');
      console.log('   2. 重新启用适配器层');
      console.log('   3. 检查SearchConfig数据完整性');
    }
    
    process.exit(1);
  } finally {
    await db.$disconnect();
  }
}

async function generateMigrationSummary(dryRun: boolean) {
  console.log('\n📊 迁移总结:');
  
  // SearchConfig统计
  const searchConfigStats = await db.$queryRaw<any[]>`
    SELECT 
      "configType", 
      "isAdvanced", 
      COUNT(*) as count
    FROM "SearchConfig" 
    WHERE "isActive" = true
    GROUP BY "configType", "isAdvanced"
    ORDER BY "configType", "isAdvanced"
  `;
  
  console.log('   SearchConfig配置:');
  let totalSearchConfigs = 0;
  searchConfigStats.forEach(stat => {
    const level = stat.isAdvanced ? '高级' : '基础';
    console.log(`     ${stat.configType} (${level}): ${stat.count} 个`);
    totalSearchConfigs += Number(stat.count);
  });
  
  // FieldConfig统计
  const fieldConfigStats = await db.fieldConfig.groupBy({
    by: ['isVisible', 'isFilterable', 'isSortable'],
    where: { isActive: true },
    _count: true
  });
  
  console.log('\n   FieldConfig保留功能:');
  fieldConfigStats.forEach(stat => {
    const features = [];
    if (stat.isVisible) features.push('可见');
    if (stat.isFilterable) features.push('可筛选');
    if (stat.isSortable) features.push('可排序');
    
    if (features.length > 0) {
      console.log(`     ${features.join('+')} 字段: ${stat._count} 个`);
    }
  });
  
  // 效益分析
  console.log('\n   📈 迁移效益:');
  console.log(`     • 统一搜索配置: ${totalSearchConfigs} 个`);
  console.log('     • 支持跨表搜索');
  console.log('     • 支持多字段搜索');
  console.log('     • 代码架构简化');
  console.log('     • 性能优化');
}

// 运行脚本
if (require.main === module) {
  main().catch(console.error);
}

export { main as executeCompleteMigration };
