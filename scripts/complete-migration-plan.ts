#!/usr/bin/env tsx

/**
 * 完整的搜索配置迁移计划
 * 将所有搜索功能从FieldConfig迁移到SearchConfig，完全丢弃旧方案
 */

import { db } from '../src/lib/prisma';

interface MigrationPlan {
  phase: string;
  description: string;
  actions: string[];
  risks: string[];
  rollbackPlan: string[];
}

const MIGRATION_PHASES: MigrationPlan[] = [
  {
    phase: "Phase 1: 数据迁移",
    description: "将现有搜索配置完全迁移到SearchConfig",
    actions: [
      "1. 运行 migrate-search-to-searchconfig.ts 完成数据迁移",
      "2. 验证所有搜索功能都有对应的SearchConfig",
      "3. 测试新配置的功能完整性",
      "4. 备份原始FieldConfig数据"
    ],
    risks: [
      "数据迁移可能遗漏某些配置",
      "新旧配置映射可能不完全准确"
    ],
    rollbackPlan: [
      "恢复FieldConfig的搜索字段",
      "重新启用适配器层"
    ]
  },
  {
    phase: "Phase 2: 代码重构",
    description: "移除所有旧搜索代码，简化架构",
    actions: [
      "1. 移除 searchConfigAdapter.ts",
      "2. 更新所有API使用SearchConfig",
      "3. 简化前端组件，移除legacy模式",
      "4. 更新缓存逻辑",
      "5. 清理FieldConfig的搜索相关字段"
    ],
    risks: [
      "可能影响现有功能",
      "前端组件需要大量更新"
    ],
    rollbackPlan: [
      "恢复适配器层代码",
      "恢复前端的hybrid模式"
    ]
  },
  {
    phase: "Phase 3: 性能优化",
    description: "优化新架构的性能",
    actions: [
      "1. 优化SearchConfig缓存策略",
      "2. 简化数据库查询",
      "3. 移除冗余的配置加载",
      "4. 优化前端组件渲染"
    ],
    risks: [
      "性能优化可能引入新bug"
    ],
    rollbackPlan: [
      "恢复原有缓存策略"
    ]
  }
];

async function analyzeMigrationFeasibility() {
  console.log('🔍 分析完整迁移的可行性...\n');

  try {
    // 1. 检查SearchConfig覆盖情况
    const searchConfigCount = await db.$queryRaw<[{count: string}]>`
      SELECT COUNT(*) as count FROM "SearchConfig" WHERE "isActive" = true
    `;
    
    const fieldConfigSearchCount = await db.fieldConfig.count({
      where: {
        isActive: true,
        OR: [
          { isSearchable: true },
          { isAdvancedSearchable: true }
        ]
      }
    });

    console.log('📊 配置覆盖分析:');
    console.log(`   SearchConfig 活跃配置: ${searchConfigCount[0].count}`);
    console.log(`   FieldConfig 搜索配置: ${fieldConfigSearchCount}`);
    
    // 2. 检查关键功能依赖
    console.log('\n🔧 关键功能依赖检查:');
    
    const filterableFields = await db.fieldConfig.count({
      where: { isActive: true, isFilterable: true }
    });
    
    const visibleFields = await db.fieldConfig.count({
      where: { isActive: true, isVisible: true }
    });
    
    const sortableFields = await db.fieldConfig.count({
      where: { isActive: true, isSortable: true }
    });

    console.log(`   筛选功能字段: ${filterableFields} (需保留)`);
    console.log(`   显示功能字段: ${visibleFields} (需保留)`);
    console.log(`   排序功能字段: ${sortableFields} (需保留)`);

    // 3. 分析代码依赖
    console.log('\n📝 代码依赖分析:');
    console.log('   需要更新的组件:');
    console.log('   • UnifiedSearchPanel → 简化为单一模式');
    console.log('   • EnhancedSearchPanel → 成为主要搜索组件');
    console.log('   • AdvancedSearch → 完全使用SearchConfig');
    console.log('   • 所有API路由 → 移除FieldConfig搜索逻辑');

    // 4. 给出建议
    console.log('\n💡 迁移建议:');
    
    if (Number(searchConfigCount[0].count) >= fieldConfigSearchCount) {
      console.log('   ✅ SearchConfig 覆盖充分，建议完全迁移');
      console.log('   ✅ 可以安全移除旧的搜索配置');
      console.log('   ✅ 代码简化效果显著');
      
      console.log('\n🚀 推荐的迁移策略:');
      console.log('   1. 立即执行完整迁移');
      console.log('   2. 保留FieldConfig的非搜索功能');
      console.log('   3. 移除所有适配器和兼容代码');
      console.log('   4. 简化前端组件架构');
      
      return true;
    } else {
      console.log('   ⚠️  SearchConfig 覆盖不足，需要先完成迁移');
      console.log('   ⚠️  建议先运行数据迁移脚本');
      
      return false;
    }

  } catch (error) {
    console.error('❌ 分析过程中发生错误:', error);
    return false;
  }
}

async function generateMigrationScript() {
  console.log('\n📋 生成迁移执行脚本...\n');
  
  MIGRATION_PHASES.forEach((phase, index) => {
    console.log(`## ${phase.phase}`);
    console.log(`**目标**: ${phase.description}\n`);
    
    console.log('**执行步骤**:');
    phase.actions.forEach(action => {
      console.log(`   ${action}`);
    });
    
    console.log('\n**风险评估**:');
    phase.risks.forEach(risk => {
      console.log(`   ⚠️  ${risk}`);
    });
    
    console.log('\n**回滚计划**:');
    phase.rollbackPlan.forEach(plan => {
      console.log(`   🔄 ${plan}`);
    });
    
    console.log('\n---\n');
  });
}

async function main() {
  console.log('🎯 搜索配置完整迁移分析\n');
  
  const feasible = await analyzeMigrationFeasibility();
  
  if (feasible) {
    await generateMigrationScript();
    
    console.log('✅ 结论: 建议执行完整迁移');
    console.log('\n📝 下一步操作:');
    console.log('   1. 运行: npm run migrate:search-config');
    console.log('   2. 测试所有搜索功能');
    console.log('   3. 执行代码重构');
    console.log('   4. 清理旧代码');
  } else {
    console.log('❌ 结论: 暂不建议完整迁移');
    console.log('\n📝 需要先完成:');
    console.log('   1. 数据迁移');
    console.log('   2. 功能验证');
    console.log('   3. 再次评估');
  }
  
  await db.$disconnect();
}

if (require.main === module) {
  main().catch(console.error);
}
