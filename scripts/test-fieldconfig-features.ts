#!/usr/bin/env tsx

/**
 * 测试 FieldConfig 各项功能是否正常工作
 * 特别是验证 todetail 字段和其他功能配置
 */

import { PrismaClient } from '@prisma/client';

const db = new PrismaClient();

async function testFieldConfigFeatures() {
  console.log('🔍 测试 FieldConfig 各项功能...\n');

  try {
    // 1. 获取所有数据库的 FieldConfig
    const databases = await db.databaseConfig.findMany({
      where: { isActive: true },
      select: { code: true, name: true }
    });

    console.log(`📊 找到 ${databases.length} 个活跃数据库\n`);

    for (const database of databases) {
      console.log(`\n🗄️  数据库: ${database.name} (${database.code})`);
      console.log('=' .repeat(50));

      // 获取该数据库的字段配置
      const fieldConfigs = await db.fieldConfig.findMany({
        where: {
          databaseCode: database.code,
          isActive: true
        },
        orderBy: [
          { listOrder: 'asc' },
          { fieldName: 'asc' }
        ]
      });

      if (fieldConfigs.length === 0) {
        console.log('❌ 没有找到字段配置');
        continue;
      }

      console.log(`📋 找到 ${fieldConfigs.length} 个字段配置\n`);

      // 2. 分析各项功能的使用情况
      const stats = {
        visible: fieldConfigs.filter(f => f.isVisible).length,
        searchable: fieldConfigs.filter(f => f.isSearchable).length,
        filterable: fieldConfigs.filter(f => f.isFilterable).length,
        sortable: fieldConfigs.filter(f => f.isSortable).length,
        todetail: fieldConfigs.filter(f => f.todetail).length,
        hasDetailOrder: fieldConfigs.filter(f => f.detailOrder > 0).length,
        statisticsEnabled: fieldConfigs.filter(f => f.isStatisticsEnabled).length,
        exportable: fieldConfigs.filter(f => f.isExportable).length
      };

      console.log('📈 功能统计:');
      console.log(`   • 可见字段 (isVisible): ${stats.visible}/${fieldConfigs.length}`);
      console.log(`   • 可搜索字段 (isSearchable): ${stats.searchable}/${fieldConfigs.length}`);
      console.log(`   • 可筛选字段 (isFilterable): ${stats.filterable}/${fieldConfigs.length}`);
      console.log(`   • 可排序字段 (isSortable): ${stats.sortable}/${fieldConfigs.length}`);
      console.log(`   • 详情链接字段 (todetail): ${stats.todetail}/${fieldConfigs.length}`);
      console.log(`   • 详情页显示字段 (detailOrder > 0): ${stats.hasDetailOrder}/${fieldConfigs.length}`);
      console.log(`   • 统计功能字段 (isStatisticsEnabled): ${stats.statisticsEnabled}/${fieldConfigs.length}`);
      console.log(`   • 可导出字段 (isExportable): ${stats.exportable}/${fieldConfigs.length}`);

      // 3. 显示 todetail 字段详情
      const todetailFields = fieldConfigs.filter(f => f.todetail);
      if (todetailFields.length > 0) {
        console.log('\n🔗 详情链接字段:');
        todetailFields.forEach(field => {
          console.log(`   • ${field.fieldName} (${field.displayName}) - 列表顺序: ${field.listOrder}`);
        });
      } else {
        console.log('\n⚠️  没有配置详情链接字段 (todetail=true)');
      }

      // 4. 显示列表页显示字段（按 listOrder 排序）
      const visibleFields = fieldConfigs
        .filter(f => f.isVisible)
        .sort((a, b) => a.listOrder - b.listOrder);
      
      if (visibleFields.length > 0) {
        console.log('\n👁️  列表页显示字段 (按 listOrder 排序):');
        visibleFields.forEach((field, index) => {
          const isTodetail = field.todetail ? ' 🔗' : '';
          const isSortable = field.isSortable ? ' ↕️' : '';
          console.log(`   ${index + 1}. ${field.fieldName} (${field.displayName})${isTodetail}${isSortable} - 顺序: ${field.listOrder}`);
        });
      }

      // 5. 显示详情页显示字段（按 detailOrder 排序）
      const detailFields = fieldConfigs
        .filter(f => f.detailOrder > 0)
        .sort((a, b) => a.detailOrder - b.detailOrder);
      
      if (detailFields.length > 0) {
        console.log('\n📄 详情页显示字段 (按 detailOrder 排序):');
        detailFields.forEach((field, index) => {
          console.log(`   ${index + 1}. ${field.fieldName} (${field.displayName}) - 顺序: ${field.detailOrder}`);
        });
      }

      // 6. 检查潜在问题
      console.log('\n🔍 潜在问题检查:');
      
      // 检查是否有重复的 listOrder
      const listOrders = visibleFields.map(f => f.listOrder);
      const duplicateListOrders = listOrders.filter((order, index) => listOrders.indexOf(order) !== index);
      if (duplicateListOrders.length > 0) {
        console.log(`   ⚠️  发现重复的 listOrder: ${[...new Set(duplicateListOrders)].join(', ')}`);
      }

      // 检查是否有重复的 detailOrder
      const detailOrders = detailFields.map(f => f.detailOrder);
      const duplicateDetailOrders = detailOrders.filter((order, index) => detailOrders.indexOf(order) !== index);
      if (duplicateDetailOrders.length > 0) {
        console.log(`   ⚠️  发现重复的 detailOrder: ${[...new Set(duplicateDetailOrders)].join(', ')}`);
      }

      // 检查是否有 todetail 字段但不可见
      const hiddenTodetailFields = fieldConfigs.filter(f => f.todetail && !f.isVisible);
      if (hiddenTodetailFields.length > 0) {
        console.log(`   ⚠️  发现隐藏的详情链接字段: ${hiddenTodetailFields.map(f => f.fieldName).join(', ')}`);
      }

      if (duplicateListOrders.length === 0 && duplicateDetailOrders.length === 0 && hiddenTodetailFields.length === 0) {
        console.log('   ✅ 没有发现明显问题');
      }
    }

    console.log('\n✅ FieldConfig 功能测试完成！');
    console.log('\n💡 修复说明:');
    console.log('   • todetail 字段现在应该在前端正确显示为链接');
    console.log('   • 其他功能 (isVisible, listOrder, isSortable, isFilterable 等) 继续正常工作');
    console.log('   • 只有搜索相关功能被迁移到 SearchConfig');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  } finally {
    await db.$disconnect();
  }
}

// 运行测试
testFieldConfigFeatures().catch(console.error);
