#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

/**
 * 初始化搜索配置表
 * 不使用Prisma迁移，直接通过SQL创建表和初始化数据
 */

async function initSearchConfig() {
  console.log('🚀 开始初始化搜索配置系统...');

  try {
    // 1. 创建搜索配置表 (如果不存在)
    console.log('📋 创建搜索配置表...');
    await db.$executeRaw`
      CREATE TABLE IF NOT EXISTS "SearchConfig" (
        "id" TEXT NOT NULL,
        "code" VARCHAR(100) NOT NULL,
        "name" VARCHAR(200) NOT NULL,
        "description" TEXT,
        "configType" TEXT NOT NULL DEFAULT 'SIMPLE_FILTER',
        "targetDatabases" JSONB NOT NULL,
        "searchFields" JSONB NOT NULL,
        "displayOrder" INTEGER NOT NULL DEFAULT 0,
        "filterType" TEXT NOT NULL DEFAULT 'input',
        "placeholder" VARCHAR(200),
        "customLogic" TEXT,
        "validationRules" JSONB,
        "options" JSONB,
        "accessLevel" VARCHAR(20) NOT NULL DEFAULT 'free',
        "isActive" BOOLEAN NOT NULL DEFAULT true,
        "isAdvanced" BOOLEAN NOT NULL DEFAULT false,
        "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
        
        CONSTRAINT "SearchConfig_pkey" PRIMARY KEY ("id")
      );
    `;

    // 2. 创建索引
    console.log('📊 创建索引...');
    await db.$executeRaw`
      CREATE UNIQUE INDEX IF NOT EXISTS "SearchConfig_code_key" ON "SearchConfig"("code");
    `;
    await db.$executeRaw`
      CREATE INDEX IF NOT EXISTS "SearchConfig_configType_idx" ON "SearchConfig"("configType");
    `;
    await db.$executeRaw`
      CREATE INDEX IF NOT EXISTS "SearchConfig_isActive_idx" ON "SearchConfig"("isActive");
    `;
    await db.$executeRaw`
      CREATE INDEX IF NOT EXISTS "SearchConfig_displayOrder_idx" ON "SearchConfig"("displayOrder");
    `;

    // 3. 检查是否已有数据
    const existingCount = await db.$queryRaw<[{count: bigint}]>`
      SELECT COUNT(*) as count FROM "SearchConfig"
    `;
    
    if (Number(existingCount[0].count) > 0) {
      console.log('⚠️  搜索配置表已有数据，跳过初始化');
      return;
    }

    // 4. 插入基础搜索配置 - 重新设计为更清晰的结构
    console.log('📝 插入基础搜索配置...');

    const basicConfigs = [
      // 单字段搜索配置 - 基于实际数据库字段
      {
        id: crypto.randomUUID(),
        code: 'us_pmn_device_name',
        name: 'Device Name (US PMN)',
        description: 'Search device names in US PMN database',
        configType: 'SIMPLE_FILTER',
        targetDatabases: JSON.stringify(['us_pmn']),
        searchFields: JSON.stringify({
          database: 'us_pmn',
          field: 'devicename',
          searchType: 'contains'
        }),
        displayOrder: 1,
        filterType: 'input',
        placeholder: 'Enter device name...',
        isActive: true,
        isAdvanced: false
      },
      {
        id: crypto.randomUUID(),
        code: 'us_pmn_applicant',
        name: 'Applicant (US PMN)',
        description: 'Search applicant names in US PMN database',
        configType: 'SIMPLE_FILTER',
        targetDatabases: JSON.stringify(['us_pmn']),
        searchFields: JSON.stringify({
          database: 'us_pmn',
          field: 'applicant',
          searchType: 'contains'
        }),
        displayOrder: 2,
        filterType: 'input',
        placeholder: 'Enter applicant name...',
        isActive: true,
        isAdvanced: false
      },
      {
        id: crypto.randomUUID(),
        code: 'us_pmn_knumber',
        name: 'K Number (US PMN)',
        description: 'Search K numbers in US PMN database',
        configType: 'SIMPLE_FILTER',
        targetDatabases: JSON.stringify(['us_pmn']),
        searchFields: JSON.stringify({
          database: 'us_pmn',
          field: 'knumber',
          searchType: 'contains'
        }),
        displayOrder: 3,
        filterType: 'input',
        placeholder: 'Enter K number...',
        isActive: true,
        isAdvanced: false
      },
      {
        id: crypto.randomUUID(),
        code: 'us_class_device_name',
        name: 'Device Name (US Class)',
        description: 'Search device names in US Class database',
        configType: 'SIMPLE_FILTER',
        targetDatabases: JSON.stringify(['us_class']),
        searchFields: JSON.stringify({
          database: 'us_class',
          field: 'devicename',
          searchType: 'contains'
        }),
        displayOrder: 3,
        filterType: 'input',
        placeholder: 'Enter device name...',
        isActive: true,
        isAdvanced: false
      },
      {
        id: crypto.randomUUID(),
        code: 'us_class_applicant',
        name: 'Applicant (US Class)',
        description: 'Search applicant names in US Class database',
        configType: 'SIMPLE_FILTER',
        targetDatabases: JSON.stringify(['us_class']),
        searchFields: JSON.stringify({
          database: 'us_class',
          field: 'applicant',
          searchType: 'contains'
        }),
        displayOrder: 4,
        filterType: 'input',
        placeholder: 'Enter applicant name...',
        isActive: true,
        isAdvanced: false
      },

      // 多字段搜索配置 - 在单个数据库中搜索多个字段
      {
        id: crypto.randomUUID(),
        code: 'us_pmn_multi_search',
        name: 'Multi-field Search (US PMN)',
        description: 'Search across multiple fields in US PMN database',
        configType: 'MULTI_FIELD',
        targetDatabases: JSON.stringify(['us_pmn']),
        searchFields: JSON.stringify({
          fields: ['devicename', 'applicant', 'knumber'],
          searchType: 'contains',
          operator: 'OR'
        }),
        displayOrder: 10,
        filterType: 'input',
        placeholder: 'Search in device, applicant, or K number...',
        isActive: true,
        isAdvanced: true
      },
      // 跨表搜索配置 - 在多个数据库中搜索相似字段
      {
        id: crypto.randomUUID(),
        code: 'cross_table_device_search',
        name: 'Device Search (All Databases)',
        description: 'Search device names across all databases',
        configType: 'CROSS_TABLE',
        targetDatabases: JSON.stringify(['us_pmn', 'us_class']),
        searchFields: JSON.stringify({
          mappings: [
            { database: 'us_pmn', field: 'devicename' },
            { database: 'us_class', field: 'devicename' }
          ],
          searchType: 'contains'
        }),
        displayOrder: 20,
        filterType: 'input',
        placeholder: 'Search devices across all databases...',
        isActive: true,
        isAdvanced: true
      },
      {
        id: crypto.randomUUID(),
        code: 'cross_table_applicant_search',
        name: 'Applicant Search (All Databases)',
        description: 'Search applicant names across all databases',
        configType: 'CROSS_TABLE',
        targetDatabases: JSON.stringify(['us_pmn', 'us_class']),
        searchFields: JSON.stringify({
          mappings: [
            { database: 'us_pmn', field: 'applicant' },
            { database: 'us_class', field: 'applicant' }
          ],
          searchType: 'contains'
        }),
        displayOrder: 21,
        filterType: 'input',
        placeholder: 'Search applicants across all databases...',
        isActive: true,
        isAdvanced: true
      }
    ];

    for (const config of basicConfigs) {
      await db.$executeRaw`
        INSERT INTO "SearchConfig" (
          "id", "code", "name", "description", "configType", "targetDatabases",
          "searchFields", "displayOrder", "filterType", "placeholder",
          "isActive", "isAdvanced", "createdAt", "updatedAt"
        ) VALUES (
          ${config.id}, ${config.code}, ${config.name}, ${config.description},
          ${config.configType}, ${config.targetDatabases}::jsonb, ${config.searchFields}::jsonb,
          ${config.displayOrder}, ${config.filterType}, ${config.placeholder},
          ${config.isActive}, ${config.isAdvanced}, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
        )
      `;
    }

    console.log('✅ 搜索配置初始化完成！');
    console.log(`📊 已创建 ${basicConfigs.length} 个基础搜索配置`);

    // 5. 验证创建结果
    const finalCount = await db.$queryRaw<[{count: bigint}]>`
      SELECT COUNT(*) as count FROM "SearchConfig"
    `;
    console.log(`🔍 当前搜索配置总数: ${Number(finalCount[0].count)}`);

  } catch (error) {
    console.error('❌ 初始化搜索配置失败:', error);
    throw error;
  }
}

// 执行初始化
if (require.main === module) {
  initSearchConfig()
    .then(() => {
      console.log('🎉 搜索配置系统初始化成功！');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 初始化失败:', error);
      process.exit(1);
    });
}

export { initSearchConfig };
