#!/usr/bin/env tsx

/**
 * 清理FieldConfig中的搜索相关字段数据
 * 只修改数据，不修改表结构
 * 保留所有其他功能字段
 */

import { db } from '../src/lib/prisma';

interface CleanupOptions {
  dryRun: boolean;
  verbose: boolean;
  database?: string;
}

async function main() {
  const options: CleanupOptions = {
    dryRun: process.argv.includes('--dry-run'),
    verbose: process.argv.includes('--verbose'),
    database: process.argv.find(arg => arg.startsWith('--database='))?.split('=')[1]
  };

  console.log('🧹 清理FieldConfig搜索字段数据');
  console.log(`模式: ${options.dryRun ? '预览模式' : '执行模式'}`);
  if (options.database) {
    console.log(`目标数据库: ${options.database}`);
  }
  console.log('');

  try {
    // 步骤1: 验证SearchConfig覆盖情况
    await verifySearchConfigCoverage(options);
    
    // 步骤2: 清理搜索相关字段
    await cleanupSearchFields(options);
    
    // 步骤3: 验证清理结果
    await verifyCleanupResult(options);
    
    console.log('\n✅ 搜索字段清理完成！');
    
    if (!options.dryRun) {
      console.log('\n📝 下一步操作：');
      console.log('1. 更新前端组件移除legacy模式');
      console.log('2. 移除searchConfigAdapter.ts');
      console.log('3. 简化API路由');
      console.log('4. 清理缓存');
    }
    
  } catch (error) {
    console.error('❌ 清理失败:', error);
    process.exit(1);
  } finally {
    await db.$disconnect();
  }
}

async function verifySearchConfigCoverage(options: CleanupOptions) {
  console.log('🔍 验证SearchConfig覆盖情况...');
  
  // 检查SearchConfig数量
  const searchConfigCount = await db.$queryRaw<[{count: string}]>`
    SELECT COUNT(*) as count FROM "SearchConfig" WHERE "isActive" = true
  `;
  
  const count = Number(searchConfigCount[0].count);
  
  if (count < 50) {
    throw new Error(`SearchConfig配置不足 (${count}个)，建议至少50个以上再进行清理`);
  }
  
  console.log(`  ✅ SearchConfig有 ${count} 个活跃配置，覆盖充分`);
  
  // 检查各数据库的覆盖情况
  const databases = ['us_pmn', 'us_class'];
  
  for (const database of databases) {
    const dbSearchConfigs = await db.$queryRaw<[{count: string}]>`
      SELECT COUNT(*) as count FROM "SearchConfig" 
      WHERE "isActive" = true 
      AND ("targetDatabases"::text LIKE '%"${database}"%' OR "configType" = 'CROSS_TABLE')
    `;
    
    const dbCount = Number(dbSearchConfigs[0].count);
    console.log(`  ✅ ${database} 数据库有 ${dbCount} 个相关搜索配置`);
  }
  
  console.log('');
}

async function cleanupSearchFields(options: CleanupOptions) {
  console.log('🧹 清理FieldConfig搜索字段...');

  const whereClause = options.database ? { databaseCode: options.database } : {};

  // 获取当前有搜索和筛选功能的字段
  const searchFields = await db.fieldConfig.findMany({
    where: {
      ...whereClause,
      isActive: true,
      OR: [
        { isSearchable: true },
        { isAdvancedSearchable: true },
        { isFilterable: true }
      ]
    },
    select: {
      id: true,
      databaseCode: true,
      fieldName: true,
      displayName: true,
      isSearchable: true,
      isAdvancedSearchable: true,
      isFilterable: true,
      searchType: true,
      filterType: true,
      filterOrder: true,
      options: true
    }
  });

  console.log(`  📊 找到 ${searchFields.length} 个有搜索/筛选功能的字段`);

  if (options.verbose) {
    searchFields.forEach(field => {
      const features = [];
      if (field.isSearchable) features.push('搜索');
      if (field.isAdvancedSearchable) features.push('高级搜索');
      if (field.isFilterable) features.push('筛选');
      console.log(`    ${field.databaseCode}.${field.fieldName} - ${features.join(', ')}`);
    });
  }

  if (options.dryRun) {
    console.log('  [预览] 将清理以下字段的搜索/筛选功能:');
    searchFields.forEach(field => {
      const features = [];
      if (field.isSearchable) features.push('搜索');
      if (field.isAdvancedSearchable) features.push('高级搜索');
      if (field.isFilterable) features.push('筛选');
      console.log(`    • ${field.databaseCode}.${field.fieldName} (${field.displayName}) - ${features.join(', ')}`);
    });
  } else {
    // 执行清理：清理所有搜索和筛选相关字段
    const updateResult = await db.fieldConfig.updateMany({
      where: {
        ...whereClause,
        isActive: true
      },
      data: {
        isSearchable: false,
        isAdvancedSearchable: false,
        isFilterable: false,
        searchType: 'contains', // 设为默认值
        filterType: 'select',   // 设为默认值
        filterOrder: 0          // 设为默认值
      }
    });

    console.log(`  ✅ 已清理 ${updateResult.count} 个字段的搜索/筛选功能`);

    // 明确说明保留的所有功能
    console.log('  📝 保留的FieldConfig功能:');
    console.log('    • 显示功能: isVisible, listOrder, detailOrder, todetail');
    console.log('    • 排序功能: isSortable, sortOrder');
    console.log('    • 统计功能: isStatisticsEnabled, statisticsOrder, statisticsType 等');
    console.log('    • 导出功能: isExportable, exportOrder, exportDisplayName');
    console.log('    • 基础配置: fieldType, displayName, validationRules');
  }

  console.log('');
}

async function verifyCleanupResult(options: CleanupOptions) {
  console.log('🔍 验证清理结果...');
  
  if (options.dryRun) {
    console.log('  [预览模式] 跳过验证');
    return;
  }
  
  const whereClause = options.database ? { databaseCode: options.database } : {};
  
  // 检查是否还有搜索/筛选字段
  const remainingSearchFields = await db.fieldConfig.count({
    where: {
      ...whereClause,
      isActive: true,
      OR: [
        { isSearchable: true },
        { isAdvancedSearchable: true },
        { isFilterable: true }
      ]
    }
  });

  if (remainingSearchFields > 0) {
    console.log(`  ⚠️  仍有 ${remainingSearchFields} 个字段有搜索/筛选功能`);
  } else {
    console.log('  ✅ 所有搜索/筛选功能已清理完成');
  }
  
  // 检查保留的功能
  const preservedStats = await db.fieldConfig.aggregate({
    where: {
      ...whereClause,
      isActive: true
    },
    _count: {
      isVisible: true,
      isSortable: true,
      isStatisticsEnabled: true,
      isExportable: true,
      todetail: true
    }
  });

  console.log('  📊 保留功能统计:');
  console.log(`    可见字段: ${preservedStats._count.isVisible} 个`);
  console.log(`    可排序字段: ${preservedStats._count.isSortable} 个`);
  console.log(`    统计功能字段: ${preservedStats._count.isStatisticsEnabled} 个`);
  console.log(`    可导出字段: ${preservedStats._count.isExportable} 个`);
  console.log(`    详情链接字段: ${preservedStats._count.todetail} 个`);
  
  console.log('');
}

// 运行脚本
if (require.main === module) {
  main().catch(console.error);
}

export { main as cleanFieldConfigSearchData };
