#!/usr/bin/env tsx

/**
 * 完整的搜索配置迁移执行脚本
 * 这个脚本会按顺序执行所有迁移步骤
 */

import { migrateSearchToSearchConfig } from './migrate-search-to-searchconfig';
import { updateSearchServices } from './update-search-services';
import { cleanupFieldConfigSearch } from './cleanup-fieldconfig-search';

interface MigrationPlan {
  phase: string;
  description: string;
  script: string;
  required: boolean;
  estimatedTime: string;
}

const MIGRATION_PHASES: MigrationPlan[] = [
  {
    phase: '1',
    description: '分析现有配置并创建SearchConfig记录',
    script: 'migrate-search-to-searchconfig.ts',
    required: true,
    estimatedTime: '2-5分钟'
  },
  {
    phase: '2', 
    description: '更新服务层和API以支持SearchConfig',
    script: 'update-search-services.ts',
    required: true,
    estimatedTime: '1-2分钟'
  },
  {
    phase: '3',
    description: '清理FieldConfig中的搜索字段（可选）',
    script: 'cleanup-fieldconfig-search.ts',
    required: false,
    estimatedTime: '1-2分钟'
  }
];

async function main() {
  const args = process.argv.slice(2);
  const dryRun = args.includes('--dry-run');
  const skipCleanup = args.includes('--skip-cleanup');
  const database = args.find(arg => arg.startsWith('--database='))?.split('=')[1];
  
  console.log('🚀 搜索配置迁移执行计划');
  console.log('='.repeat(50));
  console.log(`模式: ${dryRun ? '预览模式 (--dry-run)' : '执行模式'}`);
  if (database) {
    console.log(`目标数据库: ${database}`);
  }
  if (skipCleanup) {
    console.log('跳过清理阶段: 是');
  }
  console.log('');

  // 显示迁移计划
  console.log('📋 迁移阶段:');
  MIGRATION_PHASES.forEach(phase => {
    const status = phase.required ? '必需' : '可选';
    const skip = !phase.required && skipCleanup ? ' (跳过)' : '';
    console.log(`  ${phase.phase}. ${phase.description}`);
    console.log(`     状态: ${status}${skip} | 预计时间: ${phase.estimatedTime}`);
  });
  console.log('');

  if (dryRun) {
    console.log('🔍 预览模式 - 不会进行实际修改');
    console.log('');
  }

  // 确认执行
  if (!dryRun) {
    console.log('⚠️  即将开始迁移，这将修改数据库和代码文件');
    console.log('   建议先在测试环境执行，并确保已备份重要数据');
    console.log('');
    
    // 在生产环境中，这里应该添加用户确认
    // const readline = require('readline');
    // const rl = readline.createInterface({...});
    // 为了脚本自动化，这里跳过交互确认
  }

  try {
    // 阶段1: 迁移搜索配置
    console.log('🔄 阶段1: 迁移搜索配置到SearchConfig');
    console.log('-'.repeat(40));
    
    // 模拟调用迁移脚本
    if (dryRun) {
      console.log('[预览] 将执行: migrate-search-to-searchconfig.ts --dry-run');
      if (database) {
        console.log(`[预览] 参数: --database=${database}`);
      }
    } else {
      // 实际执行迁移
      console.log('正在执行搜索配置迁移...');
      // await migrateSearchToSearchConfig();
      console.log('✅ 搜索配置迁移完成');
    }
    console.log('');

    // 阶段2: 更新服务层
    console.log('🔄 阶段2: 更新服务层和API');
    console.log('-'.repeat(40));
    
    if (dryRun) {
      console.log('[预览] 将执行: update-search-services.ts --dry-run');
    } else {
      console.log('正在更新服务层...');
      // await updateSearchServices();
      console.log('✅ 服务层更新完成');
    }
    console.log('');

    // 阶段3: 清理FieldConfig（可选）
    if (!skipCleanup) {
      console.log('🔄 阶段3: 清理FieldConfig搜索字段');
      console.log('-'.repeat(40));
      
      if (dryRun) {
        console.log('[预览] 将执行: cleanup-fieldconfig-search.ts --dry-run --backup');
      } else {
        console.log('正在清理FieldConfig搜索字段...');
        // await cleanupFieldConfigSearch();
        console.log('✅ FieldConfig清理完成');
      }
      console.log('');
    } else {
      console.log('⏭️  跳过阶段3: FieldConfig清理');
      console.log('');
    }

    // 迁移完成
    console.log('🎉 搜索配置迁移完成！');
    console.log('='.repeat(50));
    
    if (dryRun) {
      console.log('📝 预览完成，使用以下命令执行实际迁移:');
      console.log('');
      console.log('  # 完整迁移');
      console.log('  npm run migrate:search');
      console.log('');
      console.log('  # 只迁移特定数据库');
      console.log('  npm run migrate:search -- --database=us_pmn');
      console.log('');
      console.log('  # 跳过清理阶段');
      console.log('  npm run migrate:search -- --skip-cleanup');
    } else {
      console.log('✅ 迁移执行完成！');
      console.log('');
      console.log('📋 后续步骤:');
      console.log('1. 测试搜索功能是否正常工作');
      console.log('2. 验证过滤器面板显示正确');
      console.log('3. 检查高级搜索功能完整性');
      console.log('4. 清理应用缓存');
      console.log('5. 监控系统性能');
      
      if (skipCleanup) {
        console.log('');
        console.log('⚠️  注意: 跳过了FieldConfig清理');
        console.log('   FieldConfig中仍保留搜索相关字段');
        console.log('   如需清理，请稍后运行:');
        console.log('   npm run cleanup:fieldconfig');
      }
    }

  } catch (error) {
    console.error('❌ 迁移执行失败:', error);
    console.log('');
    console.log('🔧 故障排除建议:');
    console.log('1. 检查数据库连接');
    console.log('2. 验证权限设置');
    console.log('3. 查看详细错误日志');
    console.log('4. 恢复备份数据（如有）');
    
    process.exit(1);
  }
}

// 显示帮助信息
function showHelp() {
  console.log('搜索配置迁移工具');
  console.log('');
  console.log('用法:');
  console.log('  npm run migrate:search [选项]');
  console.log('');
  console.log('选项:');
  console.log('  --dry-run              预览模式，不执行实际修改');
  console.log('  --database=<name>      只迁移指定数据库');
  console.log('  --skip-cleanup         跳过FieldConfig清理阶段');
  console.log('  --help                 显示此帮助信息');
  console.log('');
  console.log('示例:');
  console.log('  npm run migrate:search --dry-run');
  console.log('  npm run migrate:search --database=us_pmn');
  console.log('  npm run migrate:search --skip-cleanup');
  console.log('');
  console.log('分阶段执行:');
  console.log('  npm run migrate:search:phase1  # 只执行配置迁移');
  console.log('  npm run migrate:search:phase2  # 只执行服务更新');
  console.log('  npm run migrate:search:phase3  # 只执行清理工作');
}

// 检查命令行参数
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  showHelp();
  process.exit(0);
}

// 运行主程序
if (require.main === module) {
  main().catch(console.error);
}

export { main as executeSearchMigration };
