#!/usr/bin/env tsx

/**
 * 验证FieldConfig保留功能完整性
 * 确保迁移后统计、筛选、导出等功能正常
 */

import { db } from '../src/lib/prisma';

async function main() {
  console.log('🔍 验证FieldConfig保留功能完整性...\n');

  try {
    // 1. 验证统计功能
    await verifyStatisticsFunction();
    
    // 2. 验证筛选功能
    await verifyFilterFunction();
    
    // 3. 验证导出功能
    await verifyExportFunction();
    
    // 4. 验证显示功能
    await verifyDisplayFunction();
    
    // 5. 验证排序功能
    await verifySortFunction();
    
    // 6. 验证搜索功能已清理
    await verifySearchCleanup();
    
    console.log('\n✅ 所有功能验证完成！');
    
  } catch (error) {
    console.error('❌ 验证失败:', error);
    process.exit(1);
  } finally {
    await db.$disconnect();
  }
}

async function verifyStatisticsFunction() {
  console.log('📊 验证统计功能...');
  
  const statsFields = await db.fieldConfig.findMany({
    where: {
      isActive: true,
      isStatisticsEnabled: true
    },
    select: {
      databaseCode: true,
      fieldName: true,
      displayName: true,
      statisticsType: true,
      statisticsDisplayName: true,
      statisticsOrder: true,
      statisticsDefaultLimit: true,
      statisticsMaxLimit: true
    },
    orderBy: [
      { databaseCode: 'asc' },
      { statisticsOrder: 'asc' }
    ]
  });
  
  console.log(`  ✅ 找到 ${statsFields.length} 个启用统计的字段`);
  
  // 按数据库分组统计
  const statsByDb = statsFields.reduce((acc, field) => {
    if (!acc[field.databaseCode]) {
      acc[field.databaseCode] = [];
    }
    acc[field.databaseCode].push(field);
    return acc;
  }, {} as Record<string, typeof statsFields>);
  
  Object.entries(statsByDb).forEach(([dbCode, fields]) => {
    console.log(`    ${dbCode}: ${fields.length} 个统计字段`);
    fields.slice(0, 3).forEach(field => {
      console.log(`      • ${field.fieldName} (${field.statisticsType}) - ${field.displayName}`);
    });
    if (fields.length > 3) {
      console.log(`      ... 还有 ${fields.length - 3} 个字段`);
    }
  });
  
  console.log('');
}

async function verifyFilterFunction() {
  console.log('🔽 验证筛选功能...');
  
  const filterFields = await db.fieldConfig.findMany({
    where: {
      isActive: true,
      isFilterable: true
    },
    select: {
      databaseCode: true,
      fieldName: true,
      displayName: true,
      filterType: true,
      filterOrder: true,
      options: true
    },
    orderBy: [
      { databaseCode: 'asc' },
      { filterOrder: 'asc' }
    ]
  });
  
  console.log(`  ✅ 找到 ${filterFields.length} 个可筛选字段`);
  
  // 按筛选类型统计
  const filterTypeStats = filterFields.reduce((acc, field) => {
    const type = field.filterType;
    acc[type] = (acc[type] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
  
  console.log('    筛选类型分布:');
  Object.entries(filterTypeStats).forEach(([type, count]) => {
    console.log(`      ${type}: ${count} 个字段`);
  });
  
  console.log('');
}

async function verifyExportFunction() {
  console.log('📤 验证导出功能...');
  
  const exportFields = await db.fieldConfig.findMany({
    where: {
      isActive: true,
      isExportable: true
    },
    select: {
      databaseCode: true,
      fieldName: true,
      displayName: true,
      exportOrder: true,
      exportDisplayName: true
    },
    orderBy: [
      { databaseCode: 'asc' },
      { exportOrder: 'asc' }
    ]
  });
  
  console.log(`  ✅ 找到 ${exportFields.length} 个可导出字段`);
  
  // 按数据库统计
  const exportByDb = exportFields.reduce((acc, field) => {
    acc[field.databaseCode] = (acc[field.databaseCode] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
  
  console.log('    各数据库可导出字段:');
  Object.entries(exportByDb).forEach(([dbCode, count]) => {
    console.log(`      ${dbCode}: ${count} 个字段`);
  });
  
  console.log('');
}

async function verifyDisplayFunction() {
  console.log('👁️ 验证显示功能...');
  
  const displayFields = await db.fieldConfig.findMany({
    where: {
      isActive: true,
      isVisible: true
    },
    select: {
      databaseCode: true,
      fieldName: true,
      displayName: true,
      listOrder: true,
      detailOrder: true,
      todetail: true
    },
    orderBy: [
      { databaseCode: 'asc' },
      { listOrder: 'asc' }
    ]
  });
  
  console.log(`  ✅ 找到 ${displayFields.length} 个可见字段`);
  
  const detailLinkFields = displayFields.filter(f => f.todetail);
  console.log(`    其中 ${detailLinkFields.length} 个字段有详情链接功能`);
  
  // 检查listOrder是否有重复
  const orderCheck = displayFields.reduce((acc, field) => {
    const key = `${field.databaseCode}_${field.listOrder}`;
    acc[key] = (acc[key] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
  
  const duplicateOrders = Object.entries(orderCheck).filter(([_, count]) => count > 1);
  if (duplicateOrders.length > 0) {
    console.log(`    ⚠️  发现 ${duplicateOrders.length} 个重复的listOrder`);
  } else {
    console.log('    ✅ listOrder配置正常');
  }
  
  console.log('');
}

async function verifySortFunction() {
  console.log('🔄 验证排序功能...');
  
  const sortFields = await db.fieldConfig.findMany({
    where: {
      isActive: true,
      isSortable: true
    },
    select: {
      databaseCode: true,
      fieldName: true,
      displayName: true,
      sortOrder: true
    },
    orderBy: [
      { databaseCode: 'asc' },
      { sortOrder: 'asc' }
    ]
  });
  
  console.log(`  ✅ 找到 ${sortFields.length} 个可排序字段`);
  
  // 按数据库统计
  const sortByDb = sortFields.reduce((acc, field) => {
    acc[field.databaseCode] = (acc[field.databaseCode] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
  
  console.log('    各数据库可排序字段:');
  Object.entries(sortByDb).forEach(([dbCode, count]) => {
    console.log(`      ${dbCode}: ${count} 个字段`);
  });
  
  console.log('');
}

async function verifySearchCleanup() {
  console.log('🧹 验证搜索功能清理...');
  
  const searchFields = await db.fieldConfig.count({
    where: {
      isActive: true,
      OR: [
        { isSearchable: true },
        { isAdvancedSearchable: true }
      ]
    }
  });
  
  if (searchFields === 0) {
    console.log('  ✅ 搜索功能已完全清理');
  } else {
    console.log(`  ⚠️  仍有 ${searchFields} 个字段有搜索功能`);
  }
  
  // 验证SearchConfig是否正常工作
  try {
    const searchConfigCount = await db.$queryRaw<[{count: string}]>`
      SELECT COUNT(*) as count FROM "SearchConfig" WHERE "isActive" = true
    `;
    
    console.log(`  ✅ SearchConfig有 ${searchConfigCount[0].count} 个活跃配置`);
  } catch (error) {
    console.log('  ❌ SearchConfig访问失败:', error.message);
  }
  
  console.log('');
}

// 运行脚本
if (require.main === module) {
  main().catch(console.error);
}

export { main as verifyPreservedFunctions };
