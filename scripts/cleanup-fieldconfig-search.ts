#!/usr/bin/env tsx

/**
 * 清理FieldConfig中的搜索相关字段
 * 这个脚本会：
 * 1. 备份现有的搜索配置
 * 2. 从FieldConfig中移除搜索相关字段
 * 3. 更新数据库schema
 * 4. 清理相关代码引用
 */

import { db } from '../src/lib/prisma';
import { writeFileSync } from 'fs';

interface CleanupOptions {
  dryRun: boolean;
  verbose: boolean;
  backup: boolean;
}

async function main() {
  const options: CleanupOptions = {
    dryRun: process.argv.includes('--dry-run'),
    verbose: process.argv.includes('--verbose'),
    backup: process.argv.includes('--backup')
  };

  console.log('🧹 开始清理FieldConfig搜索字段...');
  console.log(`模式: ${options.dryRun ? '预览模式' : '执行模式'}`);
  console.log(`备份: ${options.backup ? '是' : '否'}`);
  console.log('');

  try {
    // 步骤1: 备份搜索配置
    if (options.backup) {
      await backupSearchConfigs(options);
    }
    
    // 步骤2: 验证SearchConfig迁移完成
    await verifySearchConfigMigration(options);
    
    // 步骤3: 更新FieldConfig表结构
    await updateFieldConfigSchema(options);
    
    // 步骤4: 清理代码引用
    await cleanupCodeReferences(options);
    
    // 步骤5: 更新接口定义
    await updateInterfaceDefinitions(options);
    
    console.log('\n✅ FieldConfig搜索字段清理完成！');
    
  } catch (error) {
    console.error('❌ 清理失败:', error);
    process.exit(1);
  } finally {
    await db.$disconnect();
  }
}

async function backupSearchConfigs(options: CleanupOptions) {
  console.log('💾 备份搜索配置...');
  
  const searchConfigs = await db.fieldConfig.findMany({
    where: {
      isActive: true,
      OR: [
        { isSearchable: true },
        { isFilterable: true },
        { isAdvancedSearchable: true }
      ]
    },
    select: {
      id: true,
      databaseCode: true,
      fieldName: true,
      displayName: true,
      isSearchable: true,
      isFilterable: true,
      isAdvancedSearchable: true,
      searchType: true,
      filterType: true,
      options: true,
      validationRules: true
    }
  });

  const backupData = {
    timestamp: new Date().toISOString(),
    totalConfigs: searchConfigs.length,
    configs: searchConfigs
  };

  const backupPath = `backup/fieldconfig-search-backup-${Date.now()}.json`;
  
  if (options.dryRun) {
    console.log(`  [预览] 将备份 ${searchConfigs.length} 个配置到: ${backupPath}`);
  } else {
    // 确保备份目录存在
    const { mkdirSync } = require('fs');
    mkdirSync('backup', { recursive: true });
    
    writeFileSync(backupPath, JSON.stringify(backupData, null, 2));
    console.log(`  ✅ 备份完成: ${backupPath}`);
    console.log(`  📊 备份了 ${searchConfigs.length} 个搜索配置`);
  }
  
  console.log('');
}

async function verifySearchConfigMigration(options: CleanupOptions) {
  console.log('🔍 验证SearchConfig迁移状态...');
  
  // 检查SearchConfig表是否存在且有数据
  const searchConfigCount = await db.$queryRaw<[{count: string}]>`
    SELECT COUNT(*) as count FROM "SearchConfig" WHERE "isActive" = true
  `;
  
  const count = Number(searchConfigCount[0].count);
  
  if (count === 0) {
    throw new Error('SearchConfig表中没有数据，请先运行迁移脚本');
  }
  
  console.log(`  ✅ SearchConfig表中有 ${count} 个活跃配置`);
  
  // 检查是否有对应的搜索配置
  const fieldConfigSearchCount = await db.fieldConfig.count({
    where: {
      isActive: true,
      OR: [
        { isSearchable: true },
        { isFilterable: true },
        { isAdvancedSearchable: true }
      ]
    }
  });
  
  console.log(`  📊 FieldConfig中有 ${fieldConfigSearchCount} 个搜索相关配置`);
  
  if (fieldConfigSearchCount > count) {
    console.log('  ⚠️  建议先完成SearchConfig迁移再进行清理');
  }
  
  console.log('');
}

async function updateFieldConfigSchema(options: CleanupOptions) {
  console.log('🗃️ 更新FieldConfig表结构...');
  
  const fieldsToRemove = [
    'isSearchable',
    'isFilterable', 
    'isAdvancedSearchable',
    'searchType'
    // 注意：filterType保留，因为它也用于显示配置
  ];
  
  if (options.dryRun) {
    console.log('  [预览] 将移除以下字段:');
    fieldsToRemove.forEach(field => {
      console.log(`    • ${field}`);
    });
  } else {
    // 注意：这里不直接删除列，而是将它们标记为废弃
    // 实际删除需要通过Prisma迁移完成
    
    console.log('  📝 标记废弃字段...');
    
    // 将所有搜索相关字段设为false（软删除）
    await db.fieldConfig.updateMany({
      where: { isActive: true },
      data: {
        isSearchable: false,
        isFilterable: false,
        isAdvancedSearchable: false
      }
    });
    
    console.log('  ✅ 搜索字段已标记为废弃');
    console.log('  📋 注意：需要通过Prisma迁移完成物理删除');
  }
  
  console.log('');
}

async function cleanupCodeReferences(options: CleanupOptions) {
  console.log('🔧 清理代码引用...');
  
  const filesToUpdate = [
    'src/lib/configCache.ts',
    'src/app/data/list/[database]/DatabasePageContent.tsx',
    'src/components/AdvancedSearch.tsx'
  ];
  
  if (options.dryRun) {
    console.log('  [预览] 需要更新的文件:');
    filesToUpdate.forEach(file => {
      console.log(`    • ${file}`);
    });
    console.log('  [预览] 需要移除的引用:');
    console.log('    • isSearchable 字段引用');
    console.log('    • isFilterable 字段引用');
    console.log('    • isAdvancedSearchable 字段引用');
    console.log('    • searchType 字段引用');
  } else {
    console.log('  📝 创建代码更新指南...');
    
    const updateGuide = `# FieldConfig搜索字段清理指南

## 需要更新的文件和内容

### 1. src/lib/configCache.ts
- 移除 DatabaseFieldConfig 接口中的搜索相关字段
- 更新 getFieldConfigs 方法，不再查询搜索字段
- 添加 @deprecated 注释提醒

### 2. src/app/data/list/[database]/DatabasePageContent.tsx  
- 移除对 isSearchable、isFilterable 的引用
- 使用 UnifiedSearchPanel 替代原有搜索组件
- 更新搜索逻辑以使用 SearchConfig

### 3. src/components/AdvancedSearch.tsx
- 更新为使用 SearchConfig API
- 移除对 FieldConfig 搜索字段的依赖
- 保持向后兼容性

### 4. 数据库Schema更新
需要创建Prisma迁移来物理删除字段：

\`\`\`prisma
// 在 schema.prisma 中移除以下字段：
model FieldConfig {
  // 移除这些字段：
  // isSearchable      Boolean    @default(false)
  // isFilterable      Boolean    @default(false) 
  // isAdvancedSearchable Boolean @default(false)
  // searchType        SearchType @default(contains)
}
\`\`\`

然后运行：
\`\`\`bash
npx prisma migrate dev --name remove-search-fields-from-fieldconfig
\`\`\`

### 5. 测试更新
- 测试所有搜索功能正常工作
- 验证过滤器面板正常显示
- 确认高级搜索功能完整

## 迁移检查清单

- [ ] 备份完成
- [ ] SearchConfig迁移验证
- [ ] 代码引用更新
- [ ] 接口定义更新  
- [ ] Prisma schema更新
- [ ] 数据库迁移执行
- [ ] 功能测试通过
- [ ] 缓存清理

## 回滚方案

如果需要回滚：
1. 恢复备份的FieldConfig数据
2. 回滚代码更改
3. 回滚数据库迁移
4. 清理SearchConfig数据（可选）
`;

    writeFileSync('FIELDCONFIG_CLEANUP_GUIDE.md', updateGuide);
    console.log('  ✅ 创建更新指南: FIELDCONFIG_CLEANUP_GUIDE.md');
  }
  
  console.log('');
}

async function updateInterfaceDefinitions(options: CleanupOptions) {
  console.log('📝 更新接口定义...');
  
  const newInterfaceContent = `// 更新后的 DatabaseFieldConfig 接口
export interface DatabaseFieldConfig {
  fieldName: string;
  displayName: string;
  fieldType: 'text' | 'date' | 'number' | 'boolean' | 'select' | 'json';
  isVisible: boolean;
  isSortable: boolean;
  sortOrder: number;
  listOrder: number;
  detailOrder: number;
  validationRules?: Record<string, unknown>;
  options?: Record<string, unknown>;
  todetail?: boolean;
  isActive?: boolean;
  
  // 移除的字段（已迁移到SearchConfig）：
  // isSearchable: boolean;
  // isFilterable: boolean; 
  // isAdvancedSearchable: boolean;
  // searchType: string;
  // filterType: string; // 部分保留用于显示配置
}

// 新的搜索配置接口（来自SearchConfig）
export interface SearchFieldConfig {
  code: string;
  name: string;
  description?: string;
  configType: 'SIMPLE_FILTER' | 'MULTI_FIELD' | 'CROSS_TABLE' | 'CUSTOM_LOGIC';
  targetDatabases: string[];
  searchFields: any;
  displayOrder: number;
  filterType: 'input' | 'select' | 'multi_select' | 'date_range' | 'checkbox' | 'range';
  placeholder?: string;
  isActive: boolean;
  isAdvanced: boolean;
}`;

  if (options.dryRun) {
    console.log('  [预览] 将更新接口定义');
    console.log('  [预览] 移除搜索相关字段');
    console.log('  [预览] 添加新的SearchFieldConfig接口');
  } else {
    writeFileSync('src/types/updated-interfaces.ts', newInterfaceContent);
    console.log('  ✅ 创建更新后的接口定义: src/types/updated-interfaces.ts');
  }
  
  console.log('');
}

// 运行脚本
if (require.main === module) {
  main().catch(console.error);
}

export { main as cleanupFieldConfigSearch };
