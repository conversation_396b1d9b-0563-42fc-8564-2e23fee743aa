#!/usr/bin/env tsx

import { 
  getAllSearchConfigs, 
  getSearchConfigsByDatabase, 
  getSearchConfigByCode,
  buildSearchWhere 
} from '../src/lib/searchConfigService';

/**
 * 测试搜索配置系统
 */

async function testSearchConfigSystem() {
  console.log('🧪 开始测试搜索配置系统...\n');

  try {
    // 1. 测试获取所有搜索配置
    console.log('📋 测试获取所有搜索配置...');
    const allConfigs = await getAllSearchConfigs();
    console.log(`✅ 成功获取 ${allConfigs.length} 个搜索配置`);
    
    allConfigs.forEach(config => {
      console.log(`  - ${config.code}: ${config.name} (${config.configType})`);
    });
    console.log();

    // 2. 测试根据数据库获取配置
    console.log('🔍 测试根据数据库获取配置...');
    const usPmnConfigs = await getSearchConfigsByDatabase('us_pmn');
    console.log(`✅ us_pmn 数据库有 ${usPmnConfigs.length} 个相关搜索配置`);
    
    usPmnConfigs.forEach(config => {
      console.log(`  - ${config.code}: ${config.name}`);
      console.log(`    目标数据库: ${config.targetDatabases.join(', ')}`);
      console.log(`    搜索字段: ${config.searchFields.fields?.length || 0} 个字段`);
    });
    console.log();

    // 3. 测试根据代码获取配置
    console.log('🎯 测试根据代码获取配置...');
    const productConfig = await getSearchConfigByCode('us_pmn_product_name');
    if (productConfig) {
      console.log(`✅ 成功获取配置: ${productConfig.name}`);
      console.log(`  描述: ${productConfig.description}`);
      console.log(`  类型: ${productConfig.configType}`);
      console.log(`  筛选器类型: ${productConfig.filterType}`);
      console.log(`  是否高级: ${productConfig.isAdvanced ? '是' : '否'}`);
    } else {
      console.log('❌ 未找到产品名称搜索配置');
    }
    console.log();

    // 4. 测试构建搜索条件
    console.log('🔧 测试构建搜索条件...');
    if (productConfig) {
      const searchWhere = buildSearchWhere(productConfig, 'test product', 'us_pmn');
      console.log('✅ 成功构建搜索条件:');
      console.log(JSON.stringify(searchWhere, null, 2));
    }
    console.log();

    // 5. 测试跨表搜索配置
    console.log('🌐 测试跨表搜索配置...');
    const globalConfig = await getSearchConfigByCode('cross_table_product_search');
    if (globalConfig) {
      console.log(`✅ 成功获取全局搜索配置: ${globalConfig.name}`);
      console.log(`  目标数据库: ${globalConfig.targetDatabases.join(', ')}`);
      console.log(`  搜索字段数量: ${globalConfig.searchFields.fields?.length || 0}`);
      
      // 测试在不同数据库中构建条件
      const usPmnWhere = buildSearchWhere(globalConfig, 'keyword', 'us_pmn');
      const usClassWhere = buildSearchWhere(globalConfig, 'keyword', 'us_class');
      
      console.log('  us_pmn 搜索条件:');
      console.log(JSON.stringify(usPmnWhere, null, 2));
      console.log('  us_class 搜索条件:');
      console.log(JSON.stringify(usClassWhere, null, 2));
    }
    console.log();

    // 6. 测试配置验证
    console.log('✅ 测试配置验证...');
    let validConfigs = 0;
    let invalidConfigs = 0;
    
    for (const config of allConfigs) {
      try {
        // 检查必要字段
        if (!config.code || !config.name || !config.configType) {
          throw new Error('缺少必要字段');
        }
        
        // 检查目标数据库
        if (!config.targetDatabases || config.targetDatabases.length === 0) {
          throw new Error('缺少目标数据库');
        }
        
        // 检查搜索字段配置 - 根据配置类型验证
        if (!config.searchFields) {
          throw new Error('缺少搜索字段配置');
        }

        if (config.configType === 'SIMPLE_FILTER') {
          if (!config.searchFields.database || !config.searchFields.field) {
            throw new Error('简单过滤器缺少数据库或字段配置');
          }
        } else if (config.configType === 'MULTI_FIELD') {
          if (!config.searchFields.fields || !Array.isArray(config.searchFields.fields)) {
            throw new Error('多字段搜索缺少字段数组');
          }
        } else if (config.configType === 'CROSS_TABLE') {
          if (!config.searchFields.mappings || !Array.isArray(config.searchFields.mappings)) {
            throw new Error('跨表搜索缺少字段映射');
          }
        }
        
        validConfigs++;
      } catch (error) {
        console.log(`❌ 配置 ${config.code} 验证失败: ${error}`);
        invalidConfigs++;
      }
    }
    
    console.log(`✅ 配置验证完成: ${validConfigs} 个有效, ${invalidConfigs} 个无效`);
    console.log();

    // 7. 性能测试
    console.log('⚡ 性能测试...');
    const startTime = Date.now();
    
    // 连续获取配置10次
    for (let i = 0; i < 10; i++) {
      await getAllSearchConfigs();
    }
    
    const endTime = Date.now();
    const avgTime = (endTime - startTime) / 10;
    
    console.log(`✅ 平均获取配置时间: ${avgTime.toFixed(2)}ms`);
    console.log();

    console.log('🎉 搜索配置系统测试完成！');
    console.log('📊 测试总结:');
    console.log(`  - 总配置数: ${allConfigs.length}`);
    console.log(`  - 有效配置: ${validConfigs}`);
    console.log(`  - 无效配置: ${invalidConfigs}`);
    console.log(`  - 平均响应时间: ${avgTime.toFixed(2)}ms`);

  } catch (error) {
    console.error('❌ 测试失败:', error);
    throw error;
  }
}

// 执行测试
if (require.main === module) {
  testSearchConfigSystem()
    .then(() => {
      console.log('\n✅ 所有测试通过！');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 测试失败:', error);
      process.exit(1);
    });
}

export { testSearchConfigSystem };
