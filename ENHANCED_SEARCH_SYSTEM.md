# 增强搜索配置系统文档

## 📋 系统概述

新的增强搜索配置系统解决了原有 `FieldConfig` 表的限制，提供了更清晰、更灵活的搜索配置方式：

### 🎯 解决的核心问题
1. **单字段限制** → **支持多字段搜索**
2. **单表限制** → **支持跨表搜索**
3. **配置僵化** → **灵活的JSON配置**
4. **管理困难** → **统一的配置管理**

### ✅ 系统特性
- 🔍 **单字段搜索**：替代原有FieldConfig，一对一字段搜索
- 🔍 **多字段搜索**：一个搜索框搜索同一数据库的多个字段
- 🌐 **跨表搜索**：在多个数据库中搜索相似字段
- ⚙️ **统一管理**：所有搜索配置在一个表中管理
- 🚀 **向后兼容**：与现有系统完全兼容

## 🏗️ 系统架构

### 核心组件

1. **SearchConfig 表**：新的搜索配置表
2. **searchConfigService**：搜索配置服务层
3. **EnhancedSearchPanel**：前端增强搜索组件
4. **Enhanced Search API**：后端搜索接口

### 数据库结构

```sql
CREATE TABLE "SearchConfig" (
  "id" TEXT PRIMARY KEY,
  "code" VARCHAR(100) UNIQUE NOT NULL,
  "name" VARCHAR(200) NOT NULL,
  "description" TEXT,
  "configType" TEXT NOT NULL DEFAULT 'SIMPLE_FILTER',
  "targetDatabases" JSONB NOT NULL,
  "searchFields" JSONB NOT NULL,
  "displayOrder" INTEGER DEFAULT 0,
  "filterType" TEXT DEFAULT 'input',
  "placeholder" VARCHAR(200),
  "customLogic" TEXT,
  "validationRules" JSONB,
  "options" JSONB,
  "accessLevel" VARCHAR(20) DEFAULT 'free',
  "isActive" BOOLEAN DEFAULT true,
  "isAdvanced" BOOLEAN DEFAULT false,
  "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🔧 配置类型详解

### 1. SIMPLE_FILTER (简单过滤器)
**用途**：单字段搜索，完全替代原有 FieldConfig 的功能
**特点**：一对一字段映射，最简单直接

**配置结构：**
```json
{
  "database": "us_pmn",
  "field": "productName",
  "searchType": "contains"
}
```

**使用场景**：
- 产品名称搜索
- 公司名称搜索
- 注册号搜索
- 任何单一字段的精确搜索

### 2. MULTI_FIELD (多字段搜索)
**用途**：在同一个数据库中搜索多个字段
**特点**：一个搜索框，多个字段同时匹配

**配置结构：**
```json
{
  "fields": ["productName", "companyName", "registrationNumber"],
  "searchType": "contains",
  "operator": "OR"
}
```

**使用场景**：
- 综合搜索：在产品名、公司名、注册号中搜索
- 模糊搜索：用户不确定要搜索哪个字段时
- 快速搜索：一次搜索覆盖多个维度

### 3. CROSS_TABLE (跨表搜索)
**用途**：在多个数据库中搜索相似的字段
**特点**：同一个搜索词在不同数据库的对应字段中搜索

**配置结构：**
```json
{
  "mappings": [
    {"database": "us_pmn", "field": "productName"},
    {"database": "us_class", "field": "devicename"}
  ],
  "searchType": "contains"
}
```

**使用场景**：
- 产品搜索：在US PMN的productName和US Class的devicename中搜索
- 公司搜索：在US PMN的companyName和US Class的applicant中搜索
- 全局搜索：跨所有数据库搜索相关信息

### 4. CUSTOM_LOGIC (自定义逻辑)
**用途**：支持复杂的自定义搜索逻辑
**特点**：最大灵活性，支持任意复杂的搜索需求
**状态**：预留接口，未来扩展使用

## 📊 已创建的基础配置

系统已自动创建了7个基础搜索配置，覆盖了所有常用搜索场景：

### 🔍 单字段搜索配置 (SIMPLE_FILTER)
1. **us_pmn_product_name** - US PMN产品名称搜索
   - 数据库：us_pmn
   - 字段：productName
   - 用途：精确搜索US PMN数据库中的产品名称

2. **us_pmn_company_name** - US PMN公司名称搜索
   - 数据库：us_pmn
   - 字段：companyName
   - 用途：精确搜索US PMN数据库中的公司名称

3. **us_class_device_name** - US Class设备名称搜索
   - 数据库：us_class
   - 字段：devicename
   - 用途：精确搜索US Class数据库中的设备名称

4. **us_class_applicant** - US Class申请人搜索
   - 数据库：us_class
   - 字段：applicant
   - 用途：精确搜索US Class数据库中的申请人

### 🔍 多字段搜索配置 (MULTI_FIELD)
5. **us_pmn_multi_search** - US PMN综合搜索 (高级)
   - 数据库：us_pmn
   - 字段：productName, companyName, registrationNumber
   - 用途：在US PMN数据库中同时搜索多个字段

### 🌐 跨表搜索配置 (CROSS_TABLE)
6. **cross_table_product_search** - 跨表产品搜索 (高级)
   - 数据库：us_pmn (productName) + us_class (devicename)
   - 用途：在所有数据库中搜索产品/设备名称

7. **cross_table_company_search** - 跨表公司搜索 (高级)
   - 数据库：us_pmn (companyName) + us_class (applicant)
   - 用途：在所有数据库中搜索公司/申请人名称

## 🚀 使用方法

### 前端使用

```tsx
import EnhancedSearchPanel from '@/components/EnhancedSearchPanel';

<EnhancedSearchPanel
  database="us_pmn"
  onSearch={handleEnhancedSearch}
  onClear={handleClearEnhancedSearch}
  loading={loading}
/>
```

### API 调用

```typescript
// 获取搜索配置
const response = await fetch(`/api/enhanced-search/${database}`);

// 执行搜索
const searchResponse = await fetch(`/api/enhanced-search/${database}`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    searchConfigs: [
      { configCode: 'product_name_search', value: 'search term' }
    ]
  })
});
```

### 服务层使用

```typescript
import { 
  getAllSearchConfigs, 
  getSearchConfigsByDatabase,
  buildSearchWhere 
} from '@/lib/searchConfigService';

// 获取配置
const configs = await getSearchConfigsByDatabase('us_pmn');

// 构建搜索条件
const where = buildSearchWhere(config, searchValue, targetDatabase);
```

## 🛠️ 管理界面

访问 `/admin/search-config` 可以：
- 查看所有搜索配置
- 创建新的搜索配置
- 编辑现有配置
- 删除不需要的配置

## 📈 性能优化

1. **缓存机制**：配置数据缓存5分钟
2. **索引优化**：关键字段已建立索引
3. **懒加载**：按需加载配置
4. **批量操作**：支持批量搜索条件

## 🔄 迁移策略

### 阶段1：并行运行
- 新旧系统同时运行
- 用户可选择使用传统筛选或增强搜索
- 逐步迁移配置

### 阶段2：功能增强
- 添加更多搜索配置
- 优化用户体验
- 收集用户反馈

### 阶段3：完全迁移
- 将所有 FieldConfig 迁移到 SearchConfig
- 移除旧系统代码
- 清理数据库

## 🧪 测试

运行测试脚本验证系统：
```bash
npx tsx scripts/test-search-config.ts
```

测试覆盖：
- ✅ 配置获取功能
- ✅ 搜索条件构建
- ✅ 跨表搜索逻辑
- ✅ 配置验证
- ✅ 性能测试

## 📝 配置示例

### 创建新的搜索配置

```typescript
const newConfig = {
  code: 'custom_search',
  name: 'Custom Search',
  description: 'Custom search configuration',
  configType: 'MULTI_FIELD',
  targetDatabases: ['us_pmn'],
  searchFields: {
    fields: [
      { database: 'us_pmn', field: 'fieldName', weight: 1.0 }
    ],
    operator: 'OR',
    searchType: 'contains'
  },
  filterType: 'input',
  placeholder: 'Enter search term...',
  isActive: true,
  isAdvanced: false
};

await createSearchConfig(newConfig);
```

## 🔍 故障排除

### 常见问题

1. **配置不显示**
   - 检查 `isActive` 状态
   - 验证 `targetDatabases` 配置
   - 清除缓存

2. **搜索无结果**
   - 检查字段名称是否正确
   - 验证数据库连接
   - 查看搜索条件构建

3. **性能问题**
   - 检查索引是否存在
   - 优化搜索字段配置
   - 调整缓存策略

### 调试工具

```typescript
// 清除缓存
import { clearSearchConfigCache } from '@/lib/searchConfigService';
clearSearchConfigCache();

// 查看构建的搜索条件
const where = buildSearchWhere(config, value, database);
console.log('Search conditions:', JSON.stringify(where, null, 2));
```

## 🎯 未来规划

1. **AI 搜索**：集成智能搜索算法
2. **搜索分析**：用户搜索行为分析
3. **自动优化**：基于使用情况自动优化配置
4. **多语言支持**：支持多语言搜索
5. **实时搜索**：实时搜索建议

## 📞 支持

如有问题或建议，请：
1. 查看本文档
2. 运行测试脚本
3. 检查日志文件
4. 联系开发团队

---

**版本**: 1.0.0  
**更新时间**: 2025-08-08  
**作者**: Augment Agent
