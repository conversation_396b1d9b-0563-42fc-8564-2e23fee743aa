"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { Filter, Plus, X, Search } from "lucide-react";

export interface SearchCondition {
  id: string;
  field: string;
  operator: string;
  value: string | { from?: string; to?: string };
  logic?: 'AND' | 'OR' | 'NOT';
}

interface AdvancedSearchProps {
  onSearch: (conditions: SearchCondition[]) => void;
  onClear: () => void;
  database?: string; // 新增：支持通过database参数自动获取字段
  availableFields?: Array<{
    fieldName: string;
    displayName: string;
    fieldType: string;
    filterType?: string;
    searchType?: string;
  }>; // 改为可选，当database参数存在时会自动获取
  currentConditions?: SearchCondition[];
  metadata?: Record<string, string[]>;
  loading?: boolean;
}

const OPERATORS = {
  text: [
    { value: 'contains', label: 'Contains' },
    { value: 'equals', label: 'Equals' },
    { value: 'startsWith', label: 'Starts with' },
    { value: 'endsWith', label: 'Ends with' },
    { value: 'notContains', label: 'Does not contain' },
  ],
  date: [
    { value: 'equals', label: 'Equals' },
    { value: 'before', label: 'Before' },
    { value: 'after', label: 'After' },
    { value: 'between', label: 'Between' },
  ],
  select: [
    { value: 'equals', label: 'Equals' },
    { value: 'notEquals', label: 'Not equals' },
  ],
  boolean: [
    { value: 'equals', label: 'Is' },
  ],
};

const LOGIC_OPERATORS = [
  { value: 'AND', label: 'AND' },
  { value: 'OR', label: 'OR' },
  { value: 'NOT', label: 'NOT' },
];

export default function AdvancedSearch({
  onSearch,
  onClear,
  database,
  availableFields: propAvailableFields,
  currentConditions = [],
  metadata = {},
  loading = false
}: AdvancedSearchProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [conditions, setConditions] = useState<SearchCondition[]>(currentConditions);
  const [availableFields, setAvailableFields] = useState<Array<{
    fieldName: string;
    displayName: string;
    fieldType: string;
    filterType?: string;
    searchType?: string;
  }>>(propAvailableFields || []);
  const [fieldsLoading, setFieldsLoading] = useState(false);

  // 当database参数存在时，通过API获取可用字段
  useEffect(() => {
    if (database && !propAvailableFields) {
      const fetchAvailableFields = async () => {
        try {
          setFieldsLoading(true);

          // 通过API获取数据库配置
          const response = await fetch(`/api/config/${database}`);
          if (!response.ok) {
            throw new Error('Failed to fetch database config');
          }

          const config = await response.json();

          // 获取所有可以用于高级搜索的字段（isSearchable 或 isFilterable 或 isAdvancedSearchable）
          const searchableFields = config.fields.filter((field: any) =>
            field.isSearchable || field.isFilterable || field.isAdvancedSearchable
          ).map((field: any) => ({
            fieldName: field.fieldName,
            displayName: field.displayName,
            fieldType: field.fieldType,
            filterType: field.filterType,
            searchType: field.searchType
          }));

          setAvailableFields(searchableFields);
        } catch (error) {
          console.error('Failed to fetch available fields:', error);
          setAvailableFields([]);
        } finally {
          setFieldsLoading(false);
        }
      };

      fetchAvailableFields();
    } else if (propAvailableFields) {
      setAvailableFields(propAvailableFields);
    }
  }, [database, propAvailableFields]);

  const addCondition = () => {
    const newCondition: SearchCondition = {
      id: Date.now().toString(),
      field: availableFields[0]?.fieldName || '',
      operator: 'contains',
      value: '',
      logic: conditions.length > 0 ? 'AND' : undefined,
    };
    setConditions([...conditions, newCondition]);
  };

  const removeCondition = (id: string) => {
    const newConditions = conditions.filter(c => c.id !== id);
    // Remove logic from first condition if it exists
    if (newConditions.length > 0 && newConditions[0].logic) {
      newConditions[0] = { ...newConditions[0], logic: undefined };
    }
    setConditions(newConditions);
  };

  const updateCondition = (id: string, updates: Partial<SearchCondition>) => {
    setConditions(conditions.map(c =>
      c.id === id ? { ...c, ...updates } : c
    ));
  };

  const handleSearch = () => {
    const validConditions = conditions.filter(c => {
      if (typeof c.value === 'string') {
        return c.value.trim() !== '';
      }
      if (typeof c.value === 'object' && c.value !== null) {
        return c.value.from || c.value.to;
      }
      return false;
    });
    onSearch(validConditions);
    setIsOpen(false);
  };

  const handleClear = () => {
    setConditions([]);
    onClear();
    setIsOpen(false);
  };

  const getOperators = (field: any) => {
    // 优先使用 searchType，然后是 fieldType
    const type = field.searchType || field.fieldType;

    switch (type) {
      case 'date_range':
      case 'date':
        return OPERATORS.date;
      case 'boolean':
        return OPERATORS.boolean;
      case 'exact':
      case 'select':
        return OPERATORS.select;
      case 'contains':
      case 'text':
      default:
        return OPERATORS.text;
    }
  };

  const renderValueInput = (condition: SearchCondition, field: any) => {
    // 处理日期范围字段或 between 操作符
    if ((field.fieldType === 'date' || field.searchType === 'date_range') && condition.operator === 'between') {
      const dateValue = typeof condition.value === 'object' ? condition.value : { from: '', to: '' };
      return (
        <DateRangePicker
          startDate={dateValue.from || ''}
          endDate={dateValue.to || ''}
          onStartDateChange={(date) => updateCondition(condition.id, {
            value: { ...dateValue, from: date }
          })}
          onEndDateChange={(date) => updateCondition(condition.id, {
            value: { ...dateValue, to: date }
          })}
          placeholder="Select date range"
          className="w-full"
        />
      );
    }

    // 处理单个日期字段
    if (field.fieldType === 'date' || field.searchType === 'date_range') {
      return (
        <Input
          type="date"
          value={typeof condition.value === 'string' ? condition.value : ''}
          onChange={(e) => updateCondition(condition.id, { value: e.target.value })}
          className="w-full"
        />
      );
    }

    if (field.fieldType === 'boolean') {
      return (
        <Select
          value={typeof condition.value === 'string' ? condition.value : '__none__'}
          onValueChange={(value) => updateCondition(condition.id, { value: value === '__none__' ? '' : value })}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select value" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="__none__">Select value</SelectItem>
            <SelectItem value="true">True</SelectItem>
            <SelectItem value="false">False</SelectItem>
          </SelectContent>
        </Select>
      );
    }

    if (field.fieldType === 'select' || field.filterType === 'select') {
      const options = metadata[field.fieldName] || [];
      const validOptions = options.filter(option => option && option.trim && option.trim() !== '');

      return (
        <Select
          value={typeof condition.value === 'string' ? condition.value : '__all__'}
          onValueChange={(value) => updateCondition(condition.id, { value: value === '__all__' ? '' : value })}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder={`Select ${field.displayName}`} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="__all__">All</SelectItem>
            {validOptions.map((option) => (
              <SelectItem key={option} value={option}>
                {option}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      );
    }

    return (
      <Input
        placeholder="Enter value"
        value={typeof condition.value === 'string' ? condition.value : ''}
        onChange={(e) => updateCondition(condition.id, { value: e.target.value })}
        className="w-full"
      />
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="h-8 text-xs" disabled={loading || fieldsLoading}>
          <Filter className="mr-1 h-3 w-3" />
          Advanced Search
          {currentConditions.length > 0 && (
            <Badge variant="secondary" className="ml-2 h-4 text-xs">
              {currentConditions.length}
            </Badge>
          )}
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Advanced Search</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {fieldsLoading ? (
            <div className="text-center py-8 text-gray-500">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p>Loading available fields...</p>
            </div>
          ) : availableFields.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Search className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No searchable fields available.</p>
              <p className="text-sm">Please check the database configuration.</p>
            </div>
          ) : conditions.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Search className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No search conditions added yet.</p>
              <p className="text-sm">Click "Add Condition" to start building your search.</p>
            </div>
          ) : (
            <div className="space-y-3">
              {conditions.map((condition, index) => {
                const field = availableFields.find(f => f.fieldName === condition.field);
                if (!field) return null;

                return (
                  <div key={condition.id} className="border rounded-lg p-4 bg-gray-50">
                    <div className="flex items-center gap-3 mb-3">
                      {index > 0 && (
                        <Select
                          value={condition.logic || 'AND'}
                          onValueChange={(value) => updateCondition(condition.id, {
                            logic: value as 'AND' | 'OR' | 'NOT'
                          })}
                        >
                          <SelectTrigger className="w-20">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {LOGIC_OPERATORS.map(op => (
                              <SelectItem key={op.value} value={op.value}>
                                {op.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeCondition(condition.id)}
                        className="ml-auto text-red-600 hover:text-red-800 hover:bg-red-50"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                      {/* Field Selection */}
                      <div>
                        <label className="block text-sm font-medium mb-1">Field</label>
                        <Select
                          value={condition.field}
                          onValueChange={(value) => updateCondition(condition.id, {
                            field: value,
                            operator: getOperators(availableFields.find(f => f.fieldName === value) || {})[0].value
                          })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {availableFields.map(field => (
                              <SelectItem key={field.fieldName} value={field.fieldName}>
                                {field.displayName}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      {/* Operator Selection */}
                      <div>
                        <label className="block text-sm font-medium mb-1">Operator</label>
                        <Select
                          value={condition.operator}
                          onValueChange={(value) => updateCondition(condition.id, { operator: value })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {getOperators(field).map(op => (
                              <SelectItem key={op.value} value={op.value}>
                                {op.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      {/* Value Input */}
                      <div>
                        <label className="block text-sm font-medium mb-1">Value</label>
                        {renderValueInput(condition, field)}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}

          <div className="flex justify-between items-center pt-4 border-t">
            <Button
              variant="outline"
              onClick={addCondition}
              className="flex items-center gap-2"
              disabled={availableFields.length === 0 || fieldsLoading}
            >
              <Plus className="h-4 w-4" />
              Add Condition
            </Button>

            <div className="flex gap-2">
              <Button variant="ghost" onClick={handleClear} disabled={loading}>
                Clear All
              </Button>
              <Button
                onClick={handleSearch}
                disabled={conditions.length === 0 || loading || fieldsLoading}
              >
                <Search className="mr-2 h-4 w-4" />
                Search
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
