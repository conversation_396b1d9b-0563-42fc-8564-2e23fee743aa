"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import EnhancedSearchPanel from './EnhancedSearchPanel';
import AdvancedSearch from './AdvancedSearch';

interface UnifiedSearchPanelProps {
  database: string;
  onSearch: (searchData: any) => void;
  onClear: () => void;
  loading?: boolean;
  className?: string;
  mode?: 'enhanced' | 'legacy' | 'hybrid' | 'auto';
  showModeSwitch?: boolean;
}

export default function UnifiedSearchPanel({
  database,
  onSearch,
  onClear,
  loading = false,
  className = "",
  mode = 'auto',
  showModeSwitch = true
}: UnifiedSearchPanelProps) {
  const [currentMode, setCurrentMode] = useState<'enhanced' | 'legacy'>(
    mode === 'enhanced' ? 'enhanced' : 'legacy'
  );
  const [searchConfigs, setSearchConfigs] = useState<any[]>([]);
  const [legacyFields, setLegacyFields] = useState<any[]>([]);
  const [configsLoading, setConfigsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadConfigurations();
  }, [database, mode]);

  const loadConfigurations = async () => {
    try {
      setConfigsLoading(true);
      setError(null);

      // 根据模式决定加载策略
      let loadMode = mode;
      if (mode === 'auto') {
        // 自动模式：先尝试加载SearchConfig，如果没有则回退到FieldConfig
        const enhancedResponse = await fetch(`/api/search-config/${database}?mode=enhanced`);
        const enhancedResult = await enhancedResponse.json();
        
        if (enhancedResult.success && enhancedResult.count > 0) {
          loadMode = 'enhanced';
          setCurrentMode('enhanced');
        } else {
          loadMode = 'legacy';
          setCurrentMode('legacy');
        }
      }

      if (loadMode === 'hybrid') {
        // 混合模式：同时加载新旧配置
        const response = await fetch(`/api/search-config/${database}?mode=hybrid`);
        const result = await response.json();
        
        if (result.success) {
          setSearchConfigs(result.data.enhanced || []);
          setLegacyFields(result.data.legacy || []);
        } else {
          throw new Error(result.error || 'Failed to load hybrid configurations');
        }
      } else if (loadMode === 'enhanced') {
        // 增强模式：只加载SearchConfig
        const response = await fetch(`/api/search-config/${database}?mode=enhanced`);
        const result = await response.json();
        
        if (result.success) {
          setSearchConfigs(result.data || []);
        } else {
          throw new Error(result.error || 'Failed to load enhanced configurations');
        }
      } else {
        // 传统模式：只加载FieldConfig
        const response = await fetch(`/api/search-config/${database}?mode=legacy`);
        const result = await response.json();
        
        if (result.success) {
          setLegacyFields(result.data || []);
        } else {
          throw new Error(result.error || 'Failed to load legacy configurations');
        }
      }
    } catch (error) {
      console.error('加载搜索配置失败:', error);
      setError(error instanceof Error ? error.message : 'Unknown error');
      
      // 错误时回退到传统模式
      setCurrentMode('legacy');
    } finally {
      setConfigsLoading(false);
    }
  };

  const handleEnhancedSearch = (searchData: any) => {
    onSearch({ 
      type: 'enhanced', 
      mode: 'searchconfig',
      data: searchData 
    });
  };

  const handleLegacySearch = (searchData: any) => {
    onSearch({ 
      type: 'legacy', 
      mode: 'fieldconfig',
      data: searchData 
    });
  };

  const handleModeSwitch = (useEnhanced: boolean) => {
    setCurrentMode(useEnhanced ? 'enhanced' : 'legacy');
  };

  if (configsLoading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-sm text-gray-600">Loading search configurations...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-center">
            <div className="text-red-600 text-sm mb-2">⚠️ Failed to load search configurations</div>
            <div className="text-xs text-gray-500">{error}</div>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={loadConfigurations}
              className="mt-2"
            >
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // 单一模式渲染
  if (mode === 'enhanced' || (mode === 'auto' && currentMode === 'enhanced')) {
    return (
      <div className={className}>
        {showModeSwitch && searchConfigs.length > 0 && (
          <div className="mb-4 flex items-center justify-between">
            <Badge variant="outline" className="text-xs">
              Enhanced Search ({searchConfigs.length} configs)
            </Badge>
          </div>
        )}
        <EnhancedSearchPanel
          database={database}
          onSearch={handleEnhancedSearch}
          onClear={onClear}
          loading={loading}
          className="border-0 shadow-none"
        />
      </div>
    );
  }

  if (mode === 'legacy' || (mode === 'auto' && currentMode === 'legacy')) {
    return (
      <div className={className}>
        {showModeSwitch && legacyFields.length > 0 && (
          <div className="mb-4 flex items-center justify-between">
            <Badge variant="outline" className="text-xs">
              Legacy Search ({legacyFields.length} fields)
            </Badge>
          </div>
        )}
        <AdvancedSearch
          database={database}
          onSearch={handleLegacySearch}
          onClear={onClear}
          loading={loading}
        />
      </div>
    );
  }

  // 混合模式渲染
  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium">Search & Filter</CardTitle>
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="text-xs">
              Hybrid Mode
            </Badge>
            {showModeSwitch && (
              <div className="flex items-center space-x-2">
                <Label htmlFor="search-mode" className="text-xs">Enhanced</Label>
                <Switch
                  id="search-mode"
                  checked={currentMode === 'enhanced'}
                  onCheckedChange={handleModeSwitch}
                  size="sm"
                />
              </div>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <Tabs value={currentMode} onValueChange={(value) => setCurrentMode(value as 'enhanced' | 'legacy')}>
          <TabsList className="grid w-full grid-cols-2 mb-4">
            <TabsTrigger value="enhanced" className="text-xs">
              Enhanced ({searchConfigs.length})
            </TabsTrigger>
            <TabsTrigger value="legacy" className="text-xs">
              Legacy ({legacyFields.length})
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="enhanced" className="mt-0">
            <EnhancedSearchPanel
              database={database}
              onSearch={handleEnhancedSearch}
              onClear={onClear}
              loading={loading}
              className="border-0 shadow-none"
            />
          </TabsContent>
          
          <TabsContent value="legacy" className="mt-0">
            <AdvancedSearch
              database={database}
              onSearch={handleLegacySearch}
              onClear={onClear}
              loading={loading}
            />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
