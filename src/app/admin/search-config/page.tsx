"use client"

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { 
  Loader2, 
  Plus, 
  Edit, 
  Trash2, 
  Settings, 
  Database,
  Search,
  Filter
} from 'lucide-react';
import { toast } from 'sonner';

interface SearchConfig {
  id: string;
  code: string;
  name: string;
  description?: string;
  configType: 'SIMPLE_FILTER' | 'MULTI_FIELD' | 'CROSS_TABLE' | 'CUSTOM_LOGIC';
  targetDatabases: string[];
  searchFields: any;
  displayOrder: number;
  filterType: 'input' | 'select' | 'multi_select' | 'date_range' | 'checkbox' | 'range';
  placeholder?: string;
  isActive: boolean;
  isAdvanced: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function SearchConfigManagement() {
  const [configs, setConfigs] = useState<SearchConfig[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingConfig, setEditingConfig] = useState<SearchConfig | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [formData, setFormData] = useState({
    code: '',
    name: '',
    description: '',
    configType: 'SIMPLE_FILTER' as const,
    targetDatabases: [] as string[],
    searchFields: '{}',
    displayOrder: 0,
    filterType: 'input' as const,
    placeholder: '',
    isActive: true,
    isAdvanced: false
  });

  useEffect(() => {
    loadConfigs();
  }, []);

  const loadConfigs = async () => {
    try {
      setLoading(true);
      // 这里需要创建一个获取所有搜索配置的API
      const response = await fetch('/api/admin/search-configs');
      if (response.ok) {
        const data = await response.json();
        setConfigs(data.configs || []);
      }
    } catch (error) {
      console.error('Failed to load configs:', error);
      toast.error('Failed to load search configurations');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      const url = editingConfig 
        ? `/api/admin/search-configs/${editingConfig.id}`
        : '/api/admin/search-configs';
      
      const method = editingConfig ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          searchFields: JSON.parse(formData.searchFields)
        }),
      });

      if (response.ok) {
        toast.success(editingConfig ? 'Configuration updated' : 'Configuration created');
        setIsDialogOpen(false);
        setEditingConfig(null);
        resetForm();
        loadConfigs();
      } else {
        const error = await response.json();
        toast.error(error.message || 'Failed to save configuration');
      }
    } catch (error) {
      console.error('Save error:', error);
      toast.error('Failed to save configuration');
    }
  };

  const handleEdit = (config: SearchConfig) => {
    setEditingConfig(config);
    setFormData({
      code: config.code,
      name: config.name,
      description: config.description || '',
      configType: config.configType,
      targetDatabases: config.targetDatabases,
      searchFields: JSON.stringify(config.searchFields, null, 2),
      displayOrder: config.displayOrder,
      filterType: config.filterType,
      placeholder: config.placeholder || '',
      isActive: config.isActive,
      isAdvanced: config.isAdvanced
    });
    setIsDialogOpen(true);
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this configuration?')) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/search-configs/${id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        toast.success('Configuration deleted');
        loadConfigs();
      } else {
        toast.error('Failed to delete configuration');
      }
    } catch (error) {
      console.error('Delete error:', error);
      toast.error('Failed to delete configuration');
    }
  };

  const resetForm = () => {
    setFormData({
      code: '',
      name: '',
      description: '',
      configType: 'SIMPLE_FILTER',
      targetDatabases: [],
      searchFields: '{}',
      displayOrder: 0,
      filterType: 'input',
      placeholder: '',
      isActive: true,
      isAdvanced: false
    });
  };

  const handleNewConfig = () => {
    setEditingConfig(null);
    resetForm();
    setIsDialogOpen(true);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading search configurations...</span>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold">Search Configuration Management</h1>
          <p className="text-gray-600 mt-2">
            Manage advanced search and filter configurations for all databases
          </p>
        </div>
        <Button onClick={handleNewConfig}>
          <Plus className="mr-2 h-4 w-4" />
          New Configuration
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Search Configurations ({configs.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Code</TableHead>
                <TableHead>Name</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Databases</TableHead>
                <TableHead>Filter Type</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {configs.map((config) => (
                <TableRow key={config.id}>
                  <TableCell className="font-mono text-sm">
                    {config.code}
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{config.name}</div>
                      {config.description && (
                        <div className="text-sm text-gray-500 truncate max-w-xs">
                          {config.description}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {config.configType.replace('_', ' ')}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {config.targetDatabases.map(db => (
                        <Badge key={db} variant="secondary" className="text-xs">
                          {db}
                        </Badge>
                      ))}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {config.filterType}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-1">
                      <Badge variant={config.isActive ? "default" : "secondary"}>
                        {config.isActive ? "Active" : "Inactive"}
                      </Badge>
                      {config.isAdvanced && (
                        <Badge variant="outline">Advanced</Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEdit(config)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDelete(config.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Edit/Create Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingConfig ? 'Edit' : 'Create'} Search Configuration
            </DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="code">Code</Label>
                <Input
                  id="code"
                  value={formData.code}
                  onChange={(e) => setFormData({...formData, code: e.target.value})}
                  placeholder="e.g., product_name_search"
                />
              </div>
              <div>
                <Label htmlFor="name">Name</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  placeholder="e.g., Product Name Search"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                placeholder="Describe what this search configuration does..."
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="configType">Configuration Type</Label>
                <Select
                  value={formData.configType}
                  onValueChange={(value: any) => setFormData({...formData, configType: value})}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="SIMPLE_FILTER">Simple Filter</SelectItem>
                    <SelectItem value="MULTI_FIELD">Multi Field</SelectItem>
                    <SelectItem value="CROSS_TABLE">Cross Table</SelectItem>
                    <SelectItem value="CUSTOM_LOGIC">Custom Logic</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="filterType">Filter Type</Label>
                <Select
                  value={formData.filterType}
                  onValueChange={(value: any) => setFormData({...formData, filterType: value})}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="input">Input</SelectItem>
                    <SelectItem value="select">Select</SelectItem>
                    <SelectItem value="multi_select">Multi Select</SelectItem>
                    <SelectItem value="date_range">Date Range</SelectItem>
                    <SelectItem value="checkbox">Checkbox</SelectItem>
                    <SelectItem value="range">Range</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="searchFields">Search Fields Configuration (JSON)</Label>
              <Textarea
                id="searchFields"
                value={formData.searchFields}
                onChange={(e) => setFormData({...formData, searchFields: e.target.value})}
                placeholder='{"fields": [{"database": "us_pmn", "field": "productName", "weight": 1.0}], "operator": "OR", "searchType": "contains"}'
                className="font-mono text-sm"
                rows={6}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="placeholder">Placeholder Text</Label>
                <Input
                  id="placeholder"
                  value={formData.placeholder}
                  onChange={(e) => setFormData({...formData, placeholder: e.target.value})}
                  placeholder="Enter placeholder text..."
                />
              </div>
              <div>
                <Label htmlFor="displayOrder">Display Order</Label>
                <Input
                  id="displayOrder"
                  type="number"
                  value={formData.displayOrder}
                  onChange={(e) => setFormData({...formData, displayOrder: parseInt(e.target.value) || 0})}
                />
              </div>
            </div>

            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-2">
                <Switch
                  id="isActive"
                  checked={formData.isActive}
                  onCheckedChange={(checked) => setFormData({...formData, isActive: checked})}
                />
                <Label htmlFor="isActive">Active</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="isAdvanced"
                  checked={formData.isAdvanced}
                  onCheckedChange={(checked) => setFormData({...formData, isAdvanced: checked})}
                />
                <Label htmlFor="isAdvanced">Advanced</Label>
              </div>
            </div>

            <div className="flex justify-end space-x-2 pt-4">
              <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleSave}>
                {editingConfig ? 'Update' : 'Create'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
