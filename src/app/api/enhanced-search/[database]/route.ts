import { type NextRequest, NextResponse } from 'next/server';
import { getDynamicModel, isPrismaModel, validateDatabaseCode } from '@/lib/dynamicTableMapping';
import { getDatabaseConfig } from '@/lib/configCache';
import { 
  getSearchConfigsByDatabase, 
  buildSearchWhere, 
  type SearchConfig 
} from '@/lib/searchConfigService';
import { validatePaginationParams, buildPaginationResponse } from '@/lib/globalPagination';

export const dynamic = 'force-dynamic';

// 增强搜索请求接口
interface EnhancedSearchRequest {
  searchConfigs: Array<{
    configCode: string;
    value: string | string[] | { from?: string; to?: string };
  }>;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * POST /api/enhanced-search/[database]
 * 基于新搜索配置系统的增强搜索API
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ database: string }> }
) {
  try {
    const { database } = await params;

    // 验证数据库代码
    const validation = await validateDatabaseCode(database);
    if (!validation.isValid) {
      return NextResponse.json(
        { success: false, error: validation.error || 'Invalid database code' },
        { status: validation.status || 400 }
      );
    }

    const body: EnhancedSearchRequest = await request.json();
    const { searchConfigs, sortBy, sortOrder = 'desc' } = body;

    // 使用全局翻页配置
    const requestedPage = body.page || 1;
    const requestedLimit = body.limit || 0;
    const { page, limit } = validatePaginationParams(requestedPage, requestedLimit);

    // 获取数据库配置
    const dbConfig = await getDatabaseConfig(database);
    const visibleFields = dbConfig.fields.filter(f => f.isVisible).map(f => f.fieldName);
    const sortableFields = dbConfig.fields.filter(f => f.isSortable).map(f => f.fieldName);

    // 获取搜索配置
    const availableConfigs = await getSearchConfigsByDatabase(database);
    const configMap = new Map(availableConfigs.map(c => [c.code, c]));

    // 构建查询条件
    const whereConditions: Record<string, unknown>[] = [];
    
    for (const searchRequest of searchConfigs) {
      const config = configMap.get(searchRequest.configCode);
      if (!config || !searchRequest.value) continue;

      // 跳过空值
      if (typeof searchRequest.value === 'string' && !searchRequest.value.trim()) continue;
      if (Array.isArray(searchRequest.value) && searchRequest.value.length === 0) continue;

      const whereCondition = buildSearchWhere(config, searchRequest.value, database);
      if (Object.keys(whereCondition).length > 0) {
        whereConditions.push(whereCondition);
      }
    }

    // 合并所有查询条件
    let finalWhere: Record<string, unknown> = {};
    if (whereConditions.length === 1) {
      finalWhere = whereConditions[0];
    } else if (whereConditions.length > 1) {
      finalWhere = { AND: whereConditions };
    }

    // 构建排序条件
    const defaultSortField = sortableFields[0] || 'id';
    const orderBy = {
      [sortBy || defaultSortField]: sortOrder,
    };

    // 使用动态模型获取数据
    const model = await getDynamicModel(database);
    if (!isPrismaModel(model)) {
      return NextResponse.json(
        { success: false, error: 'Model not found or invalid' },
        { status: 500 }
      );
    }

    // 构建 select 对象
    const select: Record<string, boolean> = {};
    visibleFields.forEach(f => { select[f] = true; });
    select['id'] = true;

    // 执行查询
    const data = await model.findMany({
      where: finalWhere,
      orderBy,
      skip: (page - 1) * limit,
      take: limit,
      select,
    }) as unknown[];

    const totalCount = await model.count({ where: finalWhere }) as number;

    // 构建响应
    const response = buildPaginationResponse({
      data,
      page,
      limit,
      totalCount
    });

    return NextResponse.json({
      success: true,
      ...response,
      searchInfo: {
        appliedConfigs: searchConfigs.filter(sc => 
          configMap.has(sc.configCode) && sc.value
        ).map(sc => ({
          configCode: sc.configCode,
          configName: configMap.get(sc.configCode)?.name,
          value: sc.value
        })),
        availableConfigs: availableConfigs.map(config => ({
          code: config.code,
          name: config.name,
          description: config.description,
          filterType: config.filterType,
          placeholder: config.placeholder,
          isAdvanced: config.isAdvanced
        })),
        totalConditions: whereConditions.length,
        finalWhere
      }
    });

  } catch (error) {
    console.error('Enhanced search error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/enhanced-search/[database]
 * 获取数据库的搜索配置信息
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ database: string }> }
) {
  try {
    const { database } = await params;

    // 验证数据库代码
    const validation = await validateDatabaseCode(database);
    if (!validation.isValid) {
      return NextResponse.json(
        { success: false, error: validation.error || 'Invalid database code' },
        { status: validation.status || 400 }
      );
    }

    // 获取搜索配置
    const searchConfigs = await getSearchConfigsByDatabase(database);
    
    // 获取数据库配置
    const dbConfig = await getDatabaseConfig(database);

    return NextResponse.json({
      success: true,
      data: {
        database,
        searchConfigs: searchConfigs.map(config => ({
          code: config.code,
          name: config.name,
          description: config.description,
          configType: config.configType,
          filterType: config.filterType,
          placeholder: config.placeholder,
          isAdvanced: config.isAdvanced,
          displayOrder: config.displayOrder,
          options: config.options,
          validationRules: config.validationRules
        })),
        basicFilters: searchConfigs.filter(c => !c.isAdvanced),
        advancedFilters: searchConfigs.filter(c => c.isAdvanced),
        databaseInfo: {
          code: database,
          name: dbConfig.name || database,
          totalFields: dbConfig.fields.length,
          searchableFields: dbConfig.fields.filter(f => f.isSearchable).length
        }
      }
    });

  } catch (error) {
    console.error('Get search configs error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
