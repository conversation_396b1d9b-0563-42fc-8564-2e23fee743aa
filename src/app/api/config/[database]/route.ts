import { NextRequest, NextResponse } from 'next/server';
import { getDatabaseConfig } from '@/lib/configCache';

export const dynamic = 'force-dynamic';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ database: string }> }
) {
  try {
    const { database } = await params;

    if (!database) {
      return NextResponse.json(
        { error: 'Database parameter is required' },
        { status: 400 }
      );
    }

    // 获取数据库配置
    const config = await getDatabaseConfig(database);

    if (!config || !config.fields || config.fields.length === 0) {
      return NextResponse.json(
        { error: 'Database configuration not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      database,
      fields: config.fields,
      defaultSort: config.defaultSort
    });

  } catch (error) {
    console.error('Error fetching database config:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
