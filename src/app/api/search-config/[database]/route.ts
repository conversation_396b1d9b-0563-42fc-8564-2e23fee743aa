import { NextRequest, NextResponse } from 'next/server';
import { getSearchConfigsByDatabase, getBasicFilterConfigs } from '@/lib/searchConfigService';
import { getHybridSearchConfig, getUnifiedSearchConfig } from '@/lib/searchConfigAdapter';

export const dynamic = 'force-dynamic';

/**
 * GET /api/search-config/[database]
 * 获取数据库的搜索配置
 * 
 * 查询参数:
 * - mode: enhanced | legacy | hybrid | unified (默认: enhanced)
 * - type: basic | advanced | filter | all (默认: all)
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ database: string }> }
) {
  try {
    const { database } = await params;
    const searchParams = request.nextUrl.searchParams;
    const mode = searchParams.get('mode') || 'enhanced';
    const type = searchParams.get('type') || 'all';
    
    console.log(`🔍 获取搜索配置: database=${database}, mode=${mode}, type=${type}`);
    
    switch (mode) {
      case 'legacy':
        // 返回传统FieldConfig格式（向后兼容）
        const { getDatabaseConfig } = await import('@/lib/configCache');
        const dbConfig = await getDatabaseConfig(database);
        
        let legacyFields = dbConfig.fields;
        if (type === 'searchable') {
          legacyFields = legacyFields.filter(f => f.isSearchable);
        } else if (type === 'filterable') {
          legacyFields = legacyFields.filter(f => f.isFilterable);
        } else if (type === 'advanced') {
          legacyFields = legacyFields.filter(f => f.isAdvancedSearchable);
        }
        
        return NextResponse.json({
          success: true,
          data: legacyFields,
          mode: 'legacy',
          type,
          count: legacyFields.length
        });
        
      case 'hybrid':
        // 返回混合配置
        const hybridConfig = await getHybridSearchConfig(database);
        return NextResponse.json({
          success: true,
          data: hybridConfig,
          mode: 'hybrid',
          counts: {
            legacy: hybridConfig.legacy.length,
            enhanced: hybridConfig.enhanced.length,
            combined: hybridConfig.combined.length
          }
        });
        
      case 'unified':
        // 返回统一配置（推荐使用）
        const unifiedConfig = await getUnifiedSearchConfig(database);
        
        let unifiedData;
        if (type === 'searchable') {
          unifiedData = unifiedConfig.searchable;
        } else if (type === 'filterable') {
          unifiedData = unifiedConfig.filterable;
        } else if (type === 'advanced') {
          unifiedData = unifiedConfig.advancedSearchable;
        } else {
          unifiedData = unifiedConfig;
        }
        
        return NextResponse.json({
          success: true,
          data: unifiedData,
          mode: 'unified',
          type,
          counts: {
            searchable: unifiedConfig.searchable.length,
            filterable: unifiedConfig.filterable.length,
            advanced: unifiedConfig.advancedSearchable.length,
            total: unifiedConfig.all.length
          }
        });
        
      case 'enhanced':
      default:
        // 返回增强SearchConfig格式
        let configs;
        
        if (type === 'basic') {
          configs = await getBasicFilterConfigs(database);
        } else if (type === 'advanced') {
          const allConfigs = await getSearchConfigsByDatabase(database);
          configs = allConfigs.filter(config => config.isAdvanced);
        } else if (type === 'filter') {
          const allConfigs = await getSearchConfigsByDatabase(database);
          configs = allConfigs.filter(config => 
            !config.isAdvanced && config.filterType !== 'input'
          );
        } else if (type === 'searchable') {
          const allConfigs = await getSearchConfigsByDatabase(database);
          configs = allConfigs.filter(config => 
            !config.isAdvanced && config.code.includes('_search')
          );
        } else {
          configs = await getSearchConfigsByDatabase(database);
        }
        
        return NextResponse.json({
          success: true,
          data: configs,
          mode: 'enhanced',
          type,
          count: configs.length,
          summary: {
            basic: configs.filter(c => !c.isAdvanced).length,
            advanced: configs.filter(c => c.isAdvanced).length,
            filters: configs.filter(c => !c.isAdvanced && c.filterType !== 'input').length,
            searches: configs.filter(c => !c.isAdvanced && c.code.includes('_search')).length
          }
        });
    }
    
  } catch (error) {
    console.error('获取搜索配置失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/search-config/[database]
 * 创建或更新搜索配置
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ database: string }> }
) {
  try {
    const { database } = await params;
    const body = await request.json();
    
    console.log(`📝 更新搜索配置: database=${database}`);
    
    // 这里可以添加创建/更新搜索配置的逻辑
    // 目前返回成功响应，实际实现可以根据需要添加
    
    return NextResponse.json({
      success: true,
      message: 'Search config updated successfully',
      database,
      data: body
    });
    
  } catch (error) {
    console.error('更新搜索配置失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/search-config/[database]
 * 删除搜索配置
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ database: string }> }
) {
  try {
    const { database } = await params;
    const searchParams = request.nextUrl.searchParams;
    const configCode = searchParams.get('code');
    
    if (!configCode) {
      return NextResponse.json(
        { success: false, error: 'Config code is required' },
        { status: 400 }
      );
    }
    
    console.log(`🗑️ 删除搜索配置: database=${database}, code=${configCode}`);
    
    // 这里可以添加删除搜索配置的逻辑
    // 目前返回成功响应
    
    return NextResponse.json({
      success: true,
      message: 'Search config deleted successfully',
      database,
      configCode
    });
    
  } catch (error) {
    console.error('删除搜索配置失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
