import { type NextRequest, NextResponse } from 'next/server';
import { 
  updateSearchConfig, 
  clearSearchConfigCache 
} from '@/lib/searchConfigService';
import { db } from '@/lib/prisma';

export const dynamic = 'force-dynamic';

/**
 * PUT /api/admin/search-configs/[id]
 * 更新搜索配置
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    
    // TODO: 添加管理员权限检查
    // const user = await getCurrentUser(request);
    // if (!user || !user.isAdmin) {
    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    // }

    const body = await request.json();
    
    // 验证ID
    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Configuration ID is required' },
        { status: 400 }
      );
    }

    // 构建更新数据
    const updateData: any = {};
    
    if (body.name !== undefined) updateData.name = body.name;
    if (body.description !== undefined) updateData.description = body.description;
    if (body.configType !== undefined) updateData.configType = body.configType;
    if (body.targetDatabases !== undefined) updateData.targetDatabases = body.targetDatabases;
    if (body.searchFields !== undefined) updateData.searchFields = body.searchFields;
    if (body.displayOrder !== undefined) updateData.displayOrder = body.displayOrder;
    if (body.filterType !== undefined) updateData.filterType = body.filterType;
    if (body.placeholder !== undefined) updateData.placeholder = body.placeholder;
    if (body.customLogic !== undefined) updateData.customLogic = body.customLogic;
    if (body.validationRules !== undefined) updateData.validationRules = body.validationRules;
    if (body.options !== undefined) updateData.options = body.options;
    if (body.accessLevel !== undefined) updateData.accessLevel = body.accessLevel;
    if (body.isActive !== undefined) updateData.isActive = body.isActive;
    if (body.isAdvanced !== undefined) updateData.isAdvanced = body.isAdvanced;

    const updatedConfig = await updateSearchConfig(id, updateData);

    if (!updatedConfig) {
      return NextResponse.json(
        { success: false, error: 'Configuration not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      config: updatedConfig,
      message: 'Search configuration updated successfully'
    });

  } catch (error) {
    console.error('Update search config error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/search-configs/[id]
 * 删除搜索配置
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    
    // TODO: 添加管理员权限检查
    // const user = await getCurrentUser(request);
    // if (!user || !user.isAdmin) {
    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    // }

    // 验证ID
    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Configuration ID is required' },
        { status: 400 }
      );
    }

    // 检查配置是否存在
    const existingConfig = await db.$queryRaw<any[]>`
      SELECT id FROM "SearchConfig" WHERE id = ${id}
    `;

    if (existingConfig.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Configuration not found' },
        { status: 404 }
      );
    }

    // 删除配置
    await db.$executeRaw`
      DELETE FROM "SearchConfig" WHERE id = ${id}
    `;

    // 清除缓存
    clearSearchConfigCache();

    return NextResponse.json({
      success: true,
      message: 'Search configuration deleted successfully'
    });

  } catch (error) {
    console.error('Delete search config error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/admin/search-configs/[id]
 * 获取单个搜索配置
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    
    // TODO: 添加管理员权限检查
    // const user = await getCurrentUser(request);
    // if (!user || !user.isAdmin) {
    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    // }

    // 验证ID
    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Configuration ID is required' },
        { status: 400 }
      );
    }

    const config = await db.$queryRaw<any[]>`
      SELECT 
        id, code, name, description, "configType", "targetDatabases",
        "searchFields", "displayOrder", "filterType", placeholder,
        "customLogic", "validationRules", options, "accessLevel",
        "isActive", "isAdvanced", "createdAt", "updatedAt"
      FROM "SearchConfig"
      WHERE id = ${id}
    `;

    if (config.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Configuration not found' },
        { status: 404 }
      );
    }

    const parsedConfig = {
      ...config[0],
      targetDatabases: typeof config[0].targetDatabases === 'string' 
        ? JSON.parse(config[0].targetDatabases) 
        : config[0].targetDatabases,
      searchFields: typeof config[0].searchFields === 'string'
        ? JSON.parse(config[0].searchFields)
        : config[0].searchFields,
      validationRules: config[0].validationRules || undefined,
      options: config[0].options || undefined
    };

    return NextResponse.json({
      success: true,
      config: parsedConfig
    });

  } catch (error) {
    console.error('Get search config error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
