import { type NextRequest, NextResponse } from 'next/server';
import { 
  getAllSearchConfigs, 
  createSearchConfig, 
  clearSearchConfigCache,
  type SearchConfig 
} from '@/lib/searchConfigService';

export const dynamic = 'force-dynamic';

/**
 * GET /api/admin/search-configs
 * 获取所有搜索配置（管理员接口）
 */
export async function GET(request: NextRequest) {
  try {
    // TODO: 添加管理员权限检查
    // const user = await getCurrentUser(request);
    // if (!user || !user.isAdmin) {
    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    // }

    const configs = await getAllSearchConfigs();
    
    return NextResponse.json({
      success: true,
      configs,
      total: configs.length
    });

  } catch (error) {
    console.error('Get search configs error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/search-configs
 * 创建新的搜索配置
 */
export async function POST(request: NextRequest) {
  try {
    // TODO: 添加管理员权限检查
    // const user = await getCurrentUser(request);
    // if (!user || !user.isAdmin) {
    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    // }

    const body = await request.json();
    
    // 验证必填字段
    if (!body.code || !body.name || !body.configType) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: code, name, configType' },
        { status: 400 }
      );
    }

    // 验证搜索字段配置
    if (!body.searchFields || typeof body.searchFields !== 'object') {
      return NextResponse.json(
        { success: false, error: 'Invalid searchFields configuration' },
        { status: 400 }
      );
    }

    // 验证目标数据库
    if (!body.targetDatabases || !Array.isArray(body.targetDatabases) || body.targetDatabases.length === 0) {
      return NextResponse.json(
        { success: false, error: 'At least one target database is required' },
        { status: 400 }
      );
    }

    const configData = {
      code: body.code,
      name: body.name,
      description: body.description || undefined,
      configType: body.configType,
      targetDatabases: body.targetDatabases,
      searchFields: body.searchFields,
      displayOrder: body.displayOrder || 0,
      filterType: body.filterType || 'input',
      placeholder: body.placeholder || undefined,
      customLogic: body.customLogic || undefined,
      validationRules: body.validationRules || undefined,
      options: body.options || undefined,
      accessLevel: body.accessLevel || 'free',
      isActive: body.isActive !== undefined ? body.isActive : true,
      isAdvanced: body.isAdvanced !== undefined ? body.isAdvanced : false
    };

    const newConfig = await createSearchConfig(configData);

    return NextResponse.json({
      success: true,
      config: newConfig,
      message: 'Search configuration created successfully'
    });

  } catch (error) {
    console.error('Create search config error:', error);
    
    // 处理唯一约束错误
    if (error instanceof Error && error.message.includes('unique constraint')) {
      return NextResponse.json(
        { success: false, error: 'Configuration code already exists' },
        { status: 409 }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
