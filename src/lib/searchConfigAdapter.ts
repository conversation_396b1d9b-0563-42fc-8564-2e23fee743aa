import { SearchConfig, getSearchConfigsByDatabase, getBasicFilterConfigs } from './searchConfigService';
import { DatabaseFieldConfig } from './configCache';

/**
 * 搜索配置适配器 - 将SearchConfig转换为兼容的FieldConfig格式
 * 用于向后兼容，逐步迁移现有代码
 */

export interface LegacySearchConfig {
  fieldName: string;
  displayName: string;
  fieldType: string;
  isSearchable: boolean;
  isFilterable: boolean;
  isAdvancedSearchable: boolean;
  searchType: string;
  filterType: string;
  options?: Record<string, unknown>;
  validationRules?: Record<string, unknown>;
  fromSearchConfig?: boolean;
  searchConfigCode?: string;
}

/**
 * 将SearchConfig转换为兼容的字段配置格式
 */
export function convertSearchConfigToFieldConfig(searchConfig: SearchConfig): LegacySearchConfig {
  // 从searchFields中提取字段信息
  const searchFields = searchConfig.searchFields as any;
  const fieldName = searchFields.field || searchFields.fields?.[0] || 'unknown';
  
  // 根据配置类型和名称判断搜索类型
  const isSearchable = !searchConfig.isAdvanced && 
                      searchConfig.configType === 'SIMPLE_FILTER' && 
                      searchConfig.code.includes('_search');
  
  const isFilterable = !searchConfig.isAdvanced && 
                      searchConfig.filterType !== 'input' && 
                      searchConfig.code.includes('_filter');
  
  const isAdvancedSearchable = searchConfig.isAdvanced;
  
  return {
    fieldName,
    displayName: searchConfig.name.replace(/ \(Advanced\)$/, '').replace(/ Search$/, '').replace(/ Filter$/, ''),
    fieldType: searchConfig.filterType === 'date_range' ? 'date' : 'text',
    isSearchable,
    isFilterable,
    isAdvancedSearchable,
    searchType: searchFields.searchType || 'contains',
    filterType: searchConfig.filterType,
    options: searchConfig.options,
    validationRules: searchConfig.validationRules,
    fromSearchConfig: true,
    searchConfigCode: searchConfig.code
  };
}

/**
 * 获取数据库的搜索字段配置（兼容模式）
 */
export async function getSearchableFields(database: string): Promise<LegacySearchConfig[]> {
  const searchConfigs = await getSearchConfigsByDatabase(database);
  return searchConfigs
    .filter(config => !config.isAdvanced && config.code.includes('_search'))
    .map(convertSearchConfigToFieldConfig);
}

/**
 * 获取数据库的过滤字段配置（兼容模式）
 */
export async function getFilterableFields(database: string): Promise<LegacySearchConfig[]> {
  const searchConfigs = await getSearchConfigsByDatabase(database);
  return searchConfigs
    .filter(config => !config.isAdvanced && config.code.includes('_filter'))
    .map(convertSearchConfigToFieldConfig);
}

/**
 * 获取数据库的高级搜索字段配置（兼容模式）
 */
export async function getAdvancedSearchableFields(database: string): Promise<LegacySearchConfig[]> {
  const searchConfigs = await getSearchConfigsByDatabase(database);
  return searchConfigs
    .filter(config => config.isAdvanced)
    .map(convertSearchConfigToFieldConfig);
}

/**
 * 混合模式：同时从FieldConfig和SearchConfig获取配置
 * 用于渐进式迁移
 */
export async function getHybridSearchConfig(database: string): Promise<{
  legacy: DatabaseFieldConfig[];
  enhanced: SearchConfig[];
  combined: LegacySearchConfig[];
}> {
  // 获取传统配置
  const { getDatabaseConfig } = await import('./configCache');
  const dbConfig = await getDatabaseConfig(database);
  const legacy = dbConfig.fields;
  
  // 获取增强配置
  const enhanced = await getSearchConfigsByDatabase(database);
  
  // 合并配置 - 优先使用SearchConfig
  const enhancedAsLegacy = enhanced.map(convertSearchConfigToFieldConfig);
  
  // 去重：如果SearchConfig中有对应字段，则不使用FieldConfig中的
  const enhancedFieldNames = new Set(enhancedAsLegacy.map(config => config.fieldName));
  const filteredLegacy = legacy.filter(config => 
    !enhancedFieldNames.has(config.fieldName) || 
    (!config.isSearchable && !config.isFilterable && !config.isAdvancedSearchable)
  );
  
  const combined = [...enhancedAsLegacy, ...filteredLegacy];
  
  return {
    legacy: filteredLegacy,
    enhanced,
    combined
  };
}

/**
 * 获取统一的搜索配置 - 优先使用SearchConfig，回退到FieldConfig
 */
export async function getUnifiedSearchConfig(database: string): Promise<{
  searchable: LegacySearchConfig[];
  filterable: LegacySearchConfig[];
  advancedSearchable: LegacySearchConfig[];
  all: LegacySearchConfig[];
}> {
  const hybrid = await getHybridSearchConfig(database);
  
  const searchable = hybrid.combined.filter(config => config.isSearchable);
  const filterable = hybrid.combined.filter(config => config.isFilterable);
  const advancedSearchable = hybrid.combined.filter(config => config.isAdvancedSearchable);
  
  return {
    searchable,
    filterable,
    advancedSearchable,
    all: hybrid.combined
  };
}

/**
 * 检查字段是否已迁移到SearchConfig
 */
export async function isFieldMigratedToSearchConfig(database: string, fieldName: string): Promise<boolean> {
  const searchConfigs = await getSearchConfigsByDatabase(database);
  return searchConfigs.some(config => {
    const searchFields = config.searchFields as any;
    return searchFields.field === fieldName || searchFields.fields?.includes(fieldName);
  });
}

/**
 * 获取字段对应的SearchConfig代码
 */
export async function getSearchConfigCodesForField(database: string, fieldName: string): Promise<string[]> {
  const searchConfigs = await getSearchConfigsByDatabase(database);
  return searchConfigs
    .filter(config => {
      const searchFields = config.searchFields as any;
      return searchFields.field === fieldName || searchFields.fields?.includes(fieldName);
    })
    .map(config => config.code);
}
