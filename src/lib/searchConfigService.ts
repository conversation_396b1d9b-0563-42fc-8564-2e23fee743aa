import { db } from './prisma';

// 搜索配置接口定义 - 重新设计为更清晰的结构
export interface SimpleFilterConfig {
  database: string;
  field: string;
  searchType: 'exact' | 'contains' | 'starts_with' | 'ends_with' | 'range';
}

export interface MultiFieldConfig {
  fields: string[];  // 同一数据库中的多个字段
  searchType: 'exact' | 'contains' | 'starts_with' | 'ends_with';
  operator: 'AND' | 'OR';
}

export interface CrossTableMapping {
  database: string;
  field: string;
}

export interface CrossTableConfig {
  mappings: CrossTableMapping[];  // 跨数据库的字段映射
  searchType: 'exact' | 'contains' | 'starts_with' | 'ends_with';
}

export type SearchConfigData = SimpleFilterConfig | MultiFieldConfig | CrossTableConfig;

export interface SearchConfig {
  id: string;
  code: string;
  name: string;
  description?: string;
  configType: 'SIMPLE_FILTER' | 'MULTI_FIELD' | 'CROSS_TABLE' | 'CUSTOM_LOGIC';
  targetDatabases: string[];
  searchFields: SearchConfigData;
  displayOrder: number;
  filterType: 'input' | 'select' | 'multi_select' | 'date_range' | 'checkbox' | 'range';
  placeholder?: string;
  customLogic?: string;
  validationRules?: Record<string, unknown>;
  options?: Record<string, unknown>;
  accessLevel: string;
  isActive: boolean;
  isAdvanced: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// 搜索配置缓存
const searchConfigCache = new Map<string, SearchConfig[]>();
const CACHE_TTL = 5 * 60 * 1000; // 5分钟缓存
let lastCacheUpdate = 0;

/**
 * 获取所有搜索配置
 */
export async function getAllSearchConfigs(): Promise<SearchConfig[]> {
  const now = Date.now();
  const cacheKey = 'all_configs';
  
  // 检查缓存
  if (searchConfigCache.has(cacheKey) && (now - lastCacheUpdate) < CACHE_TTL) {
    return searchConfigCache.get(cacheKey)!;
  }

  try {
    const configs = await db.$queryRaw<any[]>`
      SELECT 
        id, code, name, description, "configType", "targetDatabases",
        "searchFields", "displayOrder", "filterType", placeholder,
        "customLogic", "validationRules", options, "accessLevel",
        "isActive", "isAdvanced", "createdAt", "updatedAt"
      FROM "SearchConfig"
      WHERE "isActive" = true
      ORDER BY "displayOrder" ASC, name ASC
    `;

    const parsedConfigs: SearchConfig[] = configs.map(config => ({
      ...config,
      targetDatabases: typeof config.targetDatabases === 'string' 
        ? JSON.parse(config.targetDatabases) 
        : config.targetDatabases,
      searchFields: typeof config.searchFields === 'string'
        ? JSON.parse(config.searchFields)
        : config.searchFields,
      validationRules: config.validationRules || undefined,
      options: config.options || undefined
    }));

    // 更新缓存
    searchConfigCache.set(cacheKey, parsedConfigs);
    lastCacheUpdate = now;

    return parsedConfigs;
  } catch (error) {
    console.error('获取搜索配置失败:', error);
    return [];
  }
}

/**
 * 根据数据库获取相关搜索配置
 */
export async function getSearchConfigsByDatabase(database: string): Promise<SearchConfig[]> {
  const allConfigs = await getAllSearchConfigs();
  return allConfigs.filter(config => 
    config.targetDatabases.includes(database) || 
    config.configType === 'CROSS_TABLE'
  );
}

/**
 * 根据代码获取搜索配置
 */
export async function getSearchConfigByCode(code: string): Promise<SearchConfig | null> {
  const allConfigs = await getAllSearchConfigs();
  return allConfigs.find(config => config.code === code) || null;
}

/**
 * 获取基础过滤器配置（非高级搜索）
 */
export async function getBasicFilterConfigs(database: string): Promise<SearchConfig[]> {
  const configs = await getSearchConfigsByDatabase(database);
  return configs.filter(config => !config.isAdvanced);
}

/**
 * 获取高级搜索配置
 */
export async function getAdvancedSearchConfigs(database: string): Promise<SearchConfig[]> {
  const configs = await getSearchConfigsByDatabase(database);
  return configs.filter(config => config.isAdvanced);
}

/**
 * 构建搜索查询条件 - 重新设计为更清晰的逻辑
 */
export function buildSearchWhere(
  searchConfig: SearchConfig,
  searchValue: string | string[] | { from?: string; to?: string },
  targetDatabase: string
): Record<string, unknown> {
  const { searchFields, configType } = searchConfig;

  if (configType === 'SIMPLE_FILTER') {
    // 简单过滤器：单字段搜索
    const config = searchFields as SimpleFilterConfig;

    // 只处理目标数据库匹配的配置
    if (config.database !== targetDatabase) return {};

    return buildFieldCondition(config.field, searchValue, config.searchType);
  }

  if (configType === 'MULTI_FIELD') {
    // 多字段搜索：在同一数据库中搜索多个字段
    const config = searchFields as MultiFieldConfig;

    // 多字段搜索只在配置的目标数据库中生效
    if (!searchConfig.targetDatabases.includes(targetDatabase)) return {};

    const conditions = config.fields.map(fieldName =>
      buildFieldCondition(fieldName, searchValue, config.searchType)
    );

    return config.operator === 'OR'
      ? { OR: conditions }
      : { AND: conditions };
  }

  if (configType === 'CROSS_TABLE') {
    // 跨表搜索：在当前数据库中搜索对应的字段
    const config = searchFields as CrossTableConfig;

    // 找到当前数据库对应的字段映射
    const relevantMappings = config.mappings.filter(m => m.database === targetDatabase);
    if (relevantMappings.length === 0) return {};

    const conditions = relevantMappings.map(mapping =>
      buildFieldCondition(mapping.field, searchValue, config.searchType)
    );

    return conditions.length === 1
      ? conditions[0]
      : { OR: conditions };
  }

  return {};
}

/**
 * 构建单个字段的查询条件
 */
function buildFieldCondition(
  fieldName: string,
  value: string | string[] | { from?: string; to?: string },
  searchType: string
): Record<string, unknown> {
  if (typeof value === 'string') {
    switch (searchType) {
      case 'exact':
        return { [fieldName]: value };
      case 'contains':
        return { [fieldName]: { contains: value, mode: 'insensitive' } };
      case 'starts_with':
        return { [fieldName]: { startsWith: value, mode: 'insensitive' } };
      case 'ends_with':
        return { [fieldName]: { endsWith: value, mode: 'insensitive' } };
      default:
        return { [fieldName]: { contains: value, mode: 'insensitive' } };
    }
  }
  
  if (Array.isArray(value)) {
    return { [fieldName]: { in: value } };
  }
  
  if (typeof value === 'object' && value !== null) {
    const condition: Record<string, unknown> = {};
    if (value.from) {
      condition.gte = new Date(value.from);
    }
    if (value.to) {
      condition.lte = new Date(value.to);
    }
    return { [fieldName]: condition };
  }
  
  return {};
}

/**
 * 清除搜索配置缓存
 */
export function clearSearchConfigCache(): void {
  searchConfigCache.clear();
  lastCacheUpdate = 0;
}

/**
 * 创建新的搜索配置
 */
export async function createSearchConfig(config: Omit<SearchConfig, 'id' | 'createdAt' | 'updatedAt'>): Promise<SearchConfig> {
  const id = crypto.randomUUID();
  
  await db.$executeRaw`
    INSERT INTO "SearchConfig" (
      "id", "code", "name", "description", "configType", "targetDatabases",
      "searchFields", "displayOrder", "filterType", "placeholder",
      "customLogic", "validationRules", "options", "accessLevel",
      "isActive", "isAdvanced", "createdAt", "updatedAt"
    ) VALUES (
      ${id}, ${config.code}, ${config.name}, ${config.description || null},
      ${config.configType}, ${JSON.stringify(config.targetDatabases)}::jsonb,
      ${JSON.stringify(config.searchFields)}::jsonb, ${config.displayOrder},
      ${config.filterType}, ${config.placeholder || null}, ${config.customLogic || null},
      ${config.validationRules ? JSON.stringify(config.validationRules) : null}::jsonb,
      ${config.options ? JSON.stringify(config.options) : null}::jsonb,
      ${config.accessLevel}, ${config.isActive}, ${config.isAdvanced},
      CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    )
  `;
  
  // 清除缓存
  clearSearchConfigCache();
  
  // 返回创建的配置
  return await getSearchConfigByCode(config.code) as SearchConfig;
}

/**
 * 更新搜索配置
 */
export async function updateSearchConfig(id: string, updates: Partial<SearchConfig>): Promise<SearchConfig | null> {
  const setClause = [];
  const values = [];
  
  if (updates.name !== undefined) {
    setClause.push(`"name" = $${setClause.length + 1}`);
    values.push(updates.name);
  }
  
  if (updates.description !== undefined) {
    setClause.push(`"description" = $${setClause.length + 1}`);
    values.push(updates.description);
  }
  
  if (updates.targetDatabases !== undefined) {
    setClause.push(`"targetDatabases" = $${setClause.length + 1}::jsonb`);
    values.push(JSON.stringify(updates.targetDatabases));
  }
  
  if (updates.searchFields !== undefined) {
    setClause.push(`"searchFields" = $${setClause.length + 1}::jsonb`);
    values.push(JSON.stringify(updates.searchFields));
  }
  
  if (updates.isActive !== undefined) {
    setClause.push(`"isActive" = $${setClause.length + 1}`);
    values.push(updates.isActive);
  }
  
  if (setClause.length === 0) {
    return null;
  }
  
  setClause.push(`"updatedAt" = CURRENT_TIMESTAMP`);
  values.push(id);
  
  await db.$executeRaw`
    UPDATE "SearchConfig" 
    SET ${setClause.join(', ')}
    WHERE "id" = $${values.length}
  `;
  
  // 清除缓存
  clearSearchConfigCache();
  
  // 返回更新后的配置
  const config = await db.$queryRaw<any[]>`
    SELECT * FROM "SearchConfig" WHERE "id" = ${id}
  `;
  
  return config.length > 0 ? config[0] : null;
}
