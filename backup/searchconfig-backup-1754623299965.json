{"timestamp": "2025-08-08T03:21:39.965Z", "totalConfigs": 8, "configs": [{"id": "f9150424-c1ff-4bb1-b7fd-8e4796766250", "code": "us_pmn_device_name", "name": "Device Name (US PMN)", "description": "Search device names in US PMN database", "configType": "SIMPLE_FILTER", "targetDatabases": ["us_pmn"], "searchFields": {"field": "devicename", "database": "us_pmn", "searchType": "contains"}, "displayOrder": 1, "filterType": "input", "placeholder": "Enter device name...", "customLogic": null, "validationRules": null, "options": null, "accessLevel": "free", "isActive": true, "isAdvanced": false, "createdAt": "2025-08-08T09:17:16.912Z", "updatedAt": "2025-08-08T09:17:16.912Z"}, {"id": "dba7de49-2621-4331-b09c-8a2f33e79312", "code": "us_pmn_applicant", "name": "Applicant (US PMN)", "description": "Search applicant names in US PMN database", "configType": "SIMPLE_FILTER", "targetDatabases": ["us_pmn"], "searchFields": {"field": "applicant", "database": "us_pmn", "searchType": "contains"}, "displayOrder": 2, "filterType": "input", "placeholder": "Enter applicant name...", "customLogic": null, "validationRules": null, "options": null, "accessLevel": "free", "isActive": true, "isAdvanced": false, "createdAt": "2025-08-08T09:17:16.915Z", "updatedAt": "2025-08-08T09:17:16.915Z"}, {"id": "c83d502d-4d8d-4183-a620-00dbf5be5843", "code": "us_pmn_knumber", "name": "K Number (US PMN)", "description": "Search K numbers in US PMN database", "configType": "SIMPLE_FILTER", "targetDatabases": ["us_pmn"], "searchFields": {"field": "knumber", "database": "us_pmn", "searchType": "contains"}, "displayOrder": 3, "filterType": "input", "placeholder": "Enter K number...", "customLogic": null, "validationRules": null, "options": null, "accessLevel": "free", "isActive": true, "isAdvanced": false, "createdAt": "2025-08-08T09:17:16.918Z", "updatedAt": "2025-08-08T09:17:16.918Z"}, {"id": "43dcbe09-9bd4-45f3-bbe0-2228754b081a", "code": "us_class_device_name", "name": "Device Name (US Class)", "description": "Search device names in US Class database", "configType": "SIMPLE_FILTER", "targetDatabases": ["us_class"], "searchFields": {"field": "devicename", "database": "us_class", "searchType": "contains"}, "displayOrder": 3, "filterType": "input", "placeholder": "Enter device name...", "customLogic": null, "validationRules": null, "options": null, "accessLevel": "free", "isActive": true, "isAdvanced": false, "createdAt": "2025-08-08T09:17:16.921Z", "updatedAt": "2025-08-08T09:17:16.921Z"}, {"id": "f3cd62da-580c-418e-be58-b9b297abe60b", "code": "us_class_applicant", "name": "Applicant (US Class)", "description": "Search applicant names in US Class database", "configType": "SIMPLE_FILTER", "targetDatabases": ["us_class"], "searchFields": {"field": "applicant", "database": "us_class", "searchType": "contains"}, "displayOrder": 4, "filterType": "input", "placeholder": "Enter applicant name...", "customLogic": null, "validationRules": null, "options": null, "accessLevel": "free", "isActive": true, "isAdvanced": false, "createdAt": "2025-08-08T09:17:16.923Z", "updatedAt": "2025-08-08T09:17:16.923Z"}, {"id": "a78a3159-3c04-45a0-af4e-d1f5527bfa52", "code": "us_pmn_multi_search", "name": "Multi-field Search (US PMN)", "description": "Search across multiple fields in US PMN database", "configType": "MULTI_FIELD", "targetDatabases": ["us_pmn"], "searchFields": {"fields": ["devicename", "applicant", "knumber"], "operator": "OR", "searchType": "contains"}, "displayOrder": 10, "filterType": "input", "placeholder": "Search in device, applicant, or K number...", "customLogic": null, "validationRules": null, "options": null, "accessLevel": "free", "isActive": true, "isAdvanced": true, "createdAt": "2025-08-08T09:17:16.926Z", "updatedAt": "2025-08-08T09:17:16.926Z"}, {"id": "419cb6f5-59ed-4214-ad67-b99fd4062d85", "code": "cross_table_device_search", "name": "Device Search (All Databases)", "description": "Search device names across all databases", "configType": "CROSS_TABLE", "targetDatabases": ["us_pmn", "us_class"], "searchFields": {"mappings": [{"field": "devicename", "database": "us_pmn"}, {"field": "devicename", "database": "us_class"}], "searchType": "contains"}, "displayOrder": 20, "filterType": "input", "placeholder": "Search devices across all databases...", "customLogic": null, "validationRules": null, "options": null, "accessLevel": "free", "isActive": true, "isAdvanced": true, "createdAt": "2025-08-08T09:17:16.929Z", "updatedAt": "2025-08-08T09:17:16.929Z"}, {"id": "238387bf-1bdf-4b56-ba66-fa5d078e3346", "code": "cross_table_applicant_search", "name": "Applicant Search (All Databases)", "description": "Search applicant names across all databases", "configType": "CROSS_TABLE", "targetDatabases": ["us_pmn", "us_class"], "searchFields": {"mappings": [{"field": "applicant", "database": "us_pmn"}, {"field": "applicant", "database": "us_class"}], "searchType": "contains"}, "displayOrder": 21, "filterType": "input", "placeholder": "Search applicants across all databases...", "customLogic": null, "validationRules": null, "options": null, "accessLevel": "free", "isActive": true, "isAdvanced": true, "createdAt": "2025-08-08T09:17:16.931Z", "updatedAt": "2025-08-08T09:17:16.931Z"}]}