# 搜索配置迁移完整指南

## 📋 概述

这个指南将帮助你将搜索配置从 `FieldConfig` 迁移到 `SearchConfig`，实现更灵活、更强大的搜索功能。

## 🎯 迁移目标

### 当前状态 (FieldConfig)
- ✅ 基础搜索：`isSearchable` 字段
- ✅ 过滤器：`isFilterable` 字段  
- ✅ 高级搜索：`isAdvancedSearchable` 字段
- ❌ 只支持单字段搜索
- ❌ 不支持跨表搜索
- ❌ 配置分散难管理

### 目标状态 (SearchConfig)
- ✅ 统一的搜索配置管理
- ✅ 支持多字段搜索
- ✅ 支持跨表搜索
- ✅ 灵活的JSON配置
- ✅ 完整的权限控制
- ✅ 高效的缓存机制

## 🚀 执行步骤

### 1. 准备工作

```bash
# 1. 备份数据库
pg_dump your_database > backup_before_migration.sql

# 2. 确保SearchConfig表已创建
npm run init:search-config

# 3. 检查当前配置状态
npm run analyze:field-config
```

### 2. 执行迁移

#### 预览模式（推荐先执行）
```bash
# 完整预览
npm run migrate:search -- --dry-run

# 预览特定数据库
npm run migrate:search -- --dry-run --database=us_pmn

# 预览但跳过清理
npm run migrate:search -- --dry-run --skip-cleanup
```

#### 实际执行
```bash
# 完整迁移
npm run migrate:search

# 只迁移特定数据库
npm run migrate:search -- --database=us_pmn

# 跳过FieldConfig清理（推荐）
npm run migrate:search -- --skip-cleanup
```

#### 分阶段执行
```bash
# 阶段1: 创建SearchConfig记录
npm run migrate:search:phase1

# 阶段2: 更新服务层
npm run migrate:search:phase2  

# 阶段3: 清理FieldConfig（可选）
npm run migrate:search:phase3
```

### 3. 验证迁移

```bash
# 验证SearchConfig数据
npm run verify:search-config

# 测试搜索功能
npm run test:search-functions

# 检查API响应
curl "http://localhost:3000/api/search-config/us_pmn"
```

## 📦 Package.json 脚本配置

将以下脚本添加到你的 `package.json`:

```json
{
  "scripts": {
    "migrate:search": "tsx scripts/execute-search-migration.ts",
    "migrate:search:phase1": "tsx scripts/migrate-search-to-searchconfig.ts",
    "migrate:search:phase2": "tsx scripts/update-search-services.ts", 
    "migrate:search:phase3": "tsx scripts/cleanup-fieldconfig-search.ts --backup",
    "cleanup:fieldconfig": "tsx scripts/cleanup-fieldconfig-search.ts --backup",
    "init:search-config": "tsx scripts/init-search-config.ts",
    "analyze:field-config": "tsx scripts/analyze-field-config.ts",
    "verify:search-config": "tsx scripts/verify-search-config.ts"
  }
}
```

## 🔧 技术实现细节

### 1. 数据迁移映射

| FieldConfig字段 | SearchConfig映射 | 说明 |
|----------------|------------------|------|
| `isSearchable=true` | `configType: 'SIMPLE_FILTER'`, `isAdvanced: false` | 基础搜索 |
| `isFilterable=true` | `filterType: 'select'`, `isAdvanced: false` | 过滤器 |
| `isAdvancedSearchable=true` | `isAdvanced: true` | 高级搜索 |
| `searchType` | `searchFields.searchType` | 搜索类型 |
| `filterType` | `filterType` | UI组件类型 |

### 2. 配置结构对比

#### 原FieldConfig
```typescript
{
  fieldName: "productName",
  isSearchable: true,
  isFilterable: false,
  searchType: "contains",
  filterType: "input"
}
```

#### 新SearchConfig
```typescript
{
  code: "us_pmn_productName_search",
  name: "Product Name Search",
  configType: "SIMPLE_FILTER",
  targetDatabases: ["us_pmn"],
  searchFields: {
    database: "us_pmn",
    field: "productName", 
    searchType: "contains"
  },
  filterType: "input",
  isAdvanced: false
}
```

### 3. API变化

#### 原API调用
```typescript
// 获取字段配置
const config = await getDatabaseConfig(database);
const searchableFields = config.fields.filter(f => f.isSearchable);
```

#### 新API调用
```typescript
// 获取搜索配置
const searchConfigs = await getSearchConfigsByDatabase(database);
const basicConfigs = searchConfigs.filter(c => !c.isAdvanced);
```

## 🎨 前端组件更新

### 1. 使用新的统一搜索面板

```tsx
// 替换原有的搜索组件
import UnifiedSearchPanel from '@/components/UnifiedSearchPanel';

// 在组件中使用
<UnifiedSearchPanel
  database={database}
  onSearch={handleSearch}
  onClear={handleClear}
  mode="enhanced" // enhanced | legacy | hybrid
/>
```

### 2. 渐进式迁移

```tsx
// 混合模式 - 同时支持新旧配置
<UnifiedSearchPanel
  database={database}
  onSearch={handleSearch}
  onClear={handleClear}
  mode="hybrid" // 同时显示新旧搜索界面
/>
```

## 🔍 权限和缓存

### 1. 权限控制
- SearchConfig支持 `accessLevel` 字段
- 可设置 `free`、`premium`、`enterprise` 级别
- 自动继承数据库访问权限

### 2. 缓存机制
- SearchConfig有独立的缓存层
- 支持Redis缓存
- 自动缓存失效和更新

## 🚨 注意事项

### 1. 向后兼容性
- 迁移过程保持现有功能正常工作
- 提供适配器层支持旧代码
- 可以渐进式迁移，不需要一次性完成

### 2. 数据安全
- 迁移前自动备份配置数据
- 支持回滚操作
- 不会删除原有数据（除非明确指定）

### 3. 性能考虑
- SearchConfig查询经过优化
- 支持批量操作
- 缓存机制减少数据库查询

## 🔄 回滚方案

如果迁移出现问题，可以按以下步骤回滚：

```bash
# 1. 停止应用
pm2 stop your-app

# 2. 恢复数据库备份
psql your_database < backup_before_migration.sql

# 3. 回滚代码更改
git checkout HEAD~1

# 4. 清理SearchConfig数据（可选）
npm run cleanup:search-config

# 5. 重启应用
pm2 start your-app
```

## 📊 监控和验证

### 1. 功能验证清单
- [ ] 基础搜索功能正常
- [ ] 过滤器面板显示正确
- [ ] 高级搜索功能完整
- [ ] 权限控制有效
- [ ] 缓存机制工作
- [ ] API响应正确

### 2. 性能监控
- 搜索响应时间
- 数据库查询次数
- 缓存命中率
- 内存使用情况

## 🆘 故障排除

### 常见问题

1. **SearchConfig表不存在**
   ```bash
   npm run init:search-config
   ```

2. **迁移数据不完整**
   ```bash
   npm run migrate:search -- --database=specific_db
   ```

3. **前端组件报错**
   - 检查API路由是否正确
   - 验证组件导入路径
   - 确认接口定义匹配

4. **权限问题**
   - 检查数据库连接权限
   - 验证用户访问级别
   - 确认SearchConfig的accessLevel设置

## 📞 支持

如果遇到问题，请：
1. 查看详细错误日志
2. 检查数据库连接状态
3. 验证配置文件正确性
4. 联系技术支持团队
